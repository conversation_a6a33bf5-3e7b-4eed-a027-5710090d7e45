<?php

declare(strict_types=1);

namespace App\Models\UserDb\Documents\Contracts;

use Illuminate\Database\Eloquent\Model;

class ContractPlot extends Model
{
    protected $table = 'su_contracts_plots_rel';
    protected $primaryKey = 'id';

    protected $fillable = [
        'contract_id',
        'plot_id',
        'contract_area',
        'price_per_acre',
        'price_sum',
        'annex_action',
        'contract_end_date',
        'fraction',
        'percent',
        'area_for_rent',
        'rent_per_plot',
        'comment',
        'kvs_allowable_area',
        'ao_id',
        'ao_db',
        'ao_type',
    ];
}
