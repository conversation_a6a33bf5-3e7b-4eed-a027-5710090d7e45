<?php

declare(strict_types=1);

namespace App\Models\UserDb\Documents\Contracts;

use App\Models\UserDb\Layers\LayerKVS;

class SalesContract extends AbstractContractModel
{
    protected $table = 'su_sales_contracts';

    protected $primaryKey = 'id';

    // Define the fillable fields
    protected $fillable = [
        'id',
        'c_num',
        'c_date',
        'sv_num',
        'sv_date',
        'start_date',
        'renta',
        'due_date',
        'renta_nat',
        'farming_id',
        'comment',
        'agg_type',
        'active',
        'parent_id',
        'is_annex',
        'renta_nat_type_id',
        'is_sublease',
        'original_due_date',
        'original_renta',
        'original_renta_nat',
        'original_renta_nat_type_id',
        'na_num',
        'tom',
        'delo',
        'court',
        'payday',
        'farming_name',
    ];

    // Define the attributes that should be cast to native types
    protected $casts = [
        'start_date' => 'datetime',
        'due_date' => 'datetime',
        'c_date' => 'datetime',
        'sv_date' => 'datetime',
        'original_due_date' => 'datetime',
    ];

    public function plots()
    {
        return $this->hasManyThrough(LayerKVS::class, SalesContractPlot::class, 'sales_contract_id', 'gid', 'id', 'plot_id');
    }
}
