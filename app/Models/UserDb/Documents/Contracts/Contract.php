<?php

declare(strict_types=1);

namespace App\Models\UserDb\Documents\Contracts;

use App\Models\UserDb\Layers\LayerKVS;

class Contract extends AbstractContractModel
{
    protected $table = 'su_contracts';

    protected $primaryKey = 'id';

    // Define the fillable fields
    protected $fillable = [
        'c_num',
        'c_date',
        'nm_usage_rights',
        'sv_num',
        'sv_date',
        'start_date',
        'renta',
        'due_date',
        'renta_nat',
        'farming_id',
        'comment',
        'agg_type',
        'active',
        'parent_id',
        'is_annex',
        'renta_nat_type_id',
        'is_sublease',
        'original_due_date',
        'original_renta',
        'original_renta_nat',
        'original_renta_nat_type_id',
        'na_num',
        'tom',
        'delo',
        'court',
        'payday',
        'is_declaration_subleased',
        'from_sublease',
        'osz_date',
        'osz_num',
        'overall_renta',
        'is_closed_for_editing',
        'contract_price',
        'ao_c_num',
        'group',
        'farming_name',
    ];

    // Define the attributes that should be cast to native types
    protected $casts = [
        'c_date' => 'datetime',
        'sv_date' => 'datetime',
        'start_date' => 'datetime',
        'due_date' => 'datetime',
        'original_due_date' => 'datetime',
        'osz_date' => 'datetime',
    ];

    public function plots()
    {
        return $this->hasManyThrough(LayerKVS::class, ContractPlot::class, 'contract_id', 'gid', 'id', 'plot_id');
    }
}
