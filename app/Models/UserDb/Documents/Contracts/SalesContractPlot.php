<?php

declare(strict_types=1);

namespace App\Models\UserDb\Documents\Contracts;

use Illuminate\Database\Eloquent\Model;

class SalesContractPlot extends Model
{
    protected $table = 'su_sales_contracts_plots_rel';
    protected $primaryKey = 'id';
    protected $fillable = [
        'sales_contract_id',
        'contract_id',
        'sublease_contract_id',
        'plot_id',
        'contract_area',
        'contract_area_for_sale',
        'price_per_acre',
        'price_sum',
        'pc_rel_id',
    ];
}
