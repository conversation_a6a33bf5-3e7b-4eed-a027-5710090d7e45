<?php

declare(strict_types=1);

namespace App\Services\Documents\Contracts;

use App\Types\Enums\Documents\ContractTypeEnum;
use App\Models\UserDb\Documents\Contracts\SalesContract;
use App\Models\UserDb\Users\UserFarmingPermission;
use App\Services\Helper\QueryHelper;
use App\Types\DTOs\Common\ListDTO;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;

class SalesContractsService extends AbstractContractsService
{
    public function getContractsList(array $contractIds, ?int $page = 1, ?int $limit = 10, ?array $sort = null): ListDTO
    {
        /** @var \App\Models\Main\User $user */
        $user = Auth::user();
        $allowedFarmingIds = $user->getFarmingIdsByPermissions([UserFarmingPermission::PERMISSION_READ])->toArray();
        $contractTypeName = ContractTypeEnum::Sales->name();
        $contractType = ContractTypeEnum::Sales->value;
        $ownershipContractType = ContractTypeEnum::Ownership->value;
        $orderBySQL = $this->generateOrderBySQL($sort);

        // Contracts
        $contractsQuery = SalesContract::selectRaw("
            ROW_NUMBER() OVER ({$orderBySQL}) as row_num,
            su_sales_contracts.id,
            su_sales_contracts.c_num,
            {$contractType} AS c_type,
            TO_CHAR(su_sales_contracts.c_date,'DD.MM.YYYY') AS c_date,
            TO_CHAR(su_sales_contracts.start_date,'DD.MM.YYYY') AS start_date,
            su_sales_contracts.active,
            get_contract_status(su_sales_contracts.id, su_sales_contracts.active, su_sales_contracts.start_date, su_sales_contracts.due_date, false) AS status,
            '{$contractTypeName}' AS type,
            su_sales_contracts.is_sublease,
            su_sales_contracts.farming_name,
            su_sales_contracts.sv_num,
            su_sales_contracts.na_num,
            su_sales_contracts.tom,
            su_sales_contracts.delo,
            su_sales_contracts.court,
            su_sales_contracts.comment,
            su_sales_contracts.payday,
            su_sales_contracts.contract_area,
            TO_CHAR(su_sales_contracts.sv_date,'DD.MM.YYYY') AS sv_date,
            coalesce (JSON_AGG( JSON_BUILD_OBJECT( 'file_id',scfr.id,'file_name',scfr.filename ) order by scfr.id ) filter ( where scfr.id is not null), '[]'::json ) AS files,
            false as is_annex,
            false as from_sublease
        ")
            ->leftJoin('su_sales_contracts_files AS scfr', 'scfr.sales_contract_id', '=', 'su_sales_contracts.id')
            ->when(
                ! empty($contractIds),
                fn ($q) => $q->whereIn('su_sales_contracts.id', $contractIds)
            )
            ->when(
                isset($page) && isset($limit),
                fn ($q) => $q->limit($limit)->offset(($page - 1) * $limit)
            )
            ->groupBy('su_sales_contracts.id');

        // Contractors
        $contractorsQuery = DB::table('contracts AS c')
            ->selectRaw('
                sb.id,
                sb.name,
                sb.contacts,
                sscbr.sales_contract_id as contract_id
            ')
            ->join('su_sales_contracts_buyers_rel as sscbr', 'sscbr.sales_contract_id', '=', 'c.id')
            ->join('su_buyers as sb', 'sb.id', '=', 'sscbr.buyer_id');

        // Contractors list in json format
        $contractorsListJsonQuery = DB::table('contractors AS b')
            ->selectRaw('
                b.contract_id,
	            JSONB_AGG(b) AS contractors_json
            ')
            ->groupBy('b.contract_id');

        // Plots list
        $plotsListQuery = DB::table('contracts AS c')
            ->selectRaw("
                c.id as contract_id ,
                sscpr.plot_id as id,
                kad_ident ,
                lk.virtual_ekatte_name as ekatte_name ,
                lk.virtual_ntp_title as ntp_title,
                TRIM(leading 'Категория ' from lk.virtual_category_title) as category_title ,
                sscpr.contract_area,
                sscpr.contract_area_for_sale,
                lk.document_area ,
                lk.comment,
                sscpr.price_per_acre as price,
                sscpr.price_per_acre * sscpr.contract_area as price_sum,
                lk.allowable_area,
                null as area_for_rent, -- needed for FE logic
                ROUND(
                    ((sscpr.contract_area_for_sale * COALESCE(sscpr.price_per_acre, 0)) 
                    - (own_scpr.contract_area * COALESCE(own_scpr.price_per_acre, 0)))::numeric
                , 2) as plot_profit,
                COALESCE(
                    JSONB_AGG(
                        DISTINCT CASE 
                            WHEN ownership_sc.id IS NOT NULL THEN
                                JSONB_BUILD_OBJECT(
                                    'id', ownership_sc.id,
                                    'c_num', ownership_sc.c_num,
                                    'c_type', ownership_sc.nm_usage_rights,
                                    'type', ownership_sc.virtual_contract_type,
                                    'contract_area', ROUND(ownership_scpr.contract_area::numeric, 3),
                                    'is_sublease', false,
                                    'is_from_sublease', false,
                                    'start_date', TO_CHAR(ownership_sc.start_date, 'DD.MM.YYYY'),
                                    'due_date', TO_CHAR(ownership_sc.due_date, 'DD.MM.YYYY'),
                                    'status', get_contract_status(ownership_sc.id, ownership_sc.active, ownership_sc.start_date, ownership_sc.due_date)::text
                                )
                            ELSE NULL
                        END
                    ) FILTER (WHERE ownership_sc.id IS NOT NULL),
                    '[]'::jsonb
                ) AS contracts
        ")
            ->join('su_sales_contracts_plots_rel AS sscpr', 'sscpr.sales_contract_id', '=', 'c.id')
            ->join('layer_kvs AS lk', 'lk.gid', '=', 'sscpr.plot_id')
            ->join('su_contracts AS own_c', 'own_c.id', '=', 'sscpr.contract_id')
            ->join('su_contracts_plots_rel AS own_scpr', 'own_scpr.contract_id', '=', 'own_c.id')
            ->leftJoin('su_contracts_plots_rel AS ownership_scpr', function ($join) {
                $join->on('ownership_scpr.plot_id', '=', 'lk.gid')
                    ->where('ownership_scpr.annex_action', '<>', 'removed');
            })
            ->leftJoin('su_contracts AS ownership_sc', function ($join) use ($ownershipContractType, $allowedFarmingIds) {
                $join->on('ownership_sc.id', '=', 'ownership_scpr.contract_id')
                    ->where('ownership_sc.active', '=', true)
                    ->where('ownership_sc.nm_usage_rights', '=', $ownershipContractType)
                    ->whereIn('ownership_sc.farming_id', $allowedFarmingIds);
            })
            ->groupBy(
                'c.id',
                'sscpr.plot_id',
                'kad_ident',
                'lk.virtual_ekatte_name',
                'lk.virtual_ntp_title',
                'lk.virtual_category_title',
                'sscpr.contract_area',
                'sscpr.contract_area_for_sale',
                'lk.document_area',
                'lk.comment',
                'sscpr.price_per_acre',
                'lk.allowable_area',
                'own_scpr.contract_area',
                'own_scpr.price_per_acre'
            );

        // Plots list json
        $plotsListJsonQuery = DB::table('plots_list AS pl')
            ->selectRaw('
                pl.contract_id,
                JSONB_AGG(ROW_TO_JSON(pl)) AS plots_json,
                ARRAY_AGG(DISTINCT pl.ekatte_name) AS ekatte_names,
                ROUND(SUM(pl.contract_area)::numeric, 3) AS contract_area, 
                ROUND(SUM(pl.price_sum)::numeric, 3) AS total_price_sum,
                ROUND(SUM(pl.plot_profit)::numeric, 3) AS plots_profit
            ')
            ->groupBy('pl.contract_id');

        $resultQuery = DB::table('contracts', 'c')
            ->selectRaw("
                    JSONB_BUILD_OBJECT(
                        'details', row_to_json(c)::JSONB || JSONB_BUILD_OBJECT(
                        'ekatte_names', plj.ekatte_names,
                        'contract_area', c.contract_area,
                        'plots_profit', plj.plots_profit
                    ),
                        'contractors', bj.contractors_json,
                        'plots', JSONB_BUILD_OBJECT(
                            'rows', plj.plots_json,
                            'footer', JSONB_BUILD_ARRAY(
                                JSONB_BUILD_OBJECT(
                                    'title', 'Sales area',
                                    'value', COALESCE(plj.contract_area, 0)
                                ),
                                JSONB_BUILD_OBJECT(
                                    'title', 'Price/dka',
                                    'value', COALESCE(plj.total_price_sum, 0)
                                )
                            )
                        )
                    ) AS row
            ")
            ->leftJoin('contractors_list_json AS bj', 'bj.contract_id', '=', 'c.id')
            ->leftJoin('plots_list_json AS plj', 'plj.contract_id', '=', 'c.id')
            ->orderBy('c.row_num', 'asc');

        $rows = DB::table('result')
            ->withExpression('contracts', $contractsQuery)
            ->withExpression('contractors', $contractorsQuery)
            ->withExpression('contractors_list_json', $contractorsListJsonQuery)
            ->withExpression('plots_list', $plotsListQuery)
            ->withExpression('plots_list_json', $plotsListJsonQuery)
            ->withExpression('result', $resultQuery)
            ->selectRaw('
                result.row
            ')
            ->get()
            ->pluck('row')
            ->map(fn ($row) => json_decode($row, true))
            ->toArray();


        return  new ListDTO([
            'rows' => $rows,
            'total' => count($contractIds ?? []),
        ]);
    }

    public function getContractsListFooter(array $contractIds): array
    {
        $areasByContractQuery = DB::table('su_sales_contracts AS c')
            ->selectRaw('
                c.id as contract_id
                , SUM(COALESCE(sscpr.contract_area, 0)) as contract_area
                , ROUND(
                    SUM(((sscpr.contract_area_for_sale * COALESCE(sscpr.price_per_acre, 0)) 
                    - (own_scpr.contract_area * COALESCE(own_scpr.price_per_acre, 0))))::numeric
                , 2) as plot_profit
        ')
            ->join('su_sales_contracts_plots_rel AS sscpr', 'sscpr.sales_contract_id', '=', 'c.id')
            ->join('layer_kvs AS lk', 'lk.gid', '=', 'sscpr.plot_id')
            ->join('su_contracts AS own_c', 'own_c.id', '=', 'sscpr.contract_id')
            ->join('su_contracts_plots_rel AS own_scpr', 'own_scpr.contract_id', '=', 'own_c.id')
            ->when(
                ! empty($contractIds),
                fn ($q) => $q->whereIn('c.id', $contractIds)
            )
            ->groupBy('c.id');

        $footer = DB::table('areas_by_contract')
            ->withExpression('areas_by_contract', $areasByContractQuery)
            ->selectRaw('
                SUM(contract_area) as total_contract_area,
                SUM(plot_profit) as total_plots_profit
            ')
            ->first();

        return (array) $footer;
    }

    private function generateOrderBySQL(?array $sort): string
    {
        $columnMapFn = fn ($sortKey) => match($sortKey) {
            'id' => 'su_sales_contracts.id',
            'c_num' => 'su_sales_contracts.c_num',
            'start_date' => 'su_sales_contracts.start_date',
            'farming_name' => 'su_sales_contracts.farming_name',
            'contract_area' => 'su_sales_contracts.contract_area',
            default => throw new InvalidArgumentException("Invalid sort column: {$sortKey}"),
        };


        $sort = array_map(
            fn ($sortOptions) => [
                'column' => $columnMapFn($sortOptions['column']),
                'direction' => $sortOptions['direction'],
            ],
            $sort ?? []
        );

        return QueryHelper::generateOrderBySQL($sort);
    }
}
