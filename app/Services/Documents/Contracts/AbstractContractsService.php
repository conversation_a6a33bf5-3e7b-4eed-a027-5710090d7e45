<?php

declare(strict_types=1);

namespace App\Services\Documents\Contracts;

use App\Models\UserDb\Documents\Contracts\AbstractContractModel;
use App\Types\DTOs\Common\ListDTO;

abstract class AbstractContractsService
{
    abstract public function getContractsList(array $contractIds, ?int $page = 1, ?int $limit = 10, ?array $sort = null): ListDTO;

    abstract public function getContractsListFooter(array $contractIds): ?array;

    public function getContractDetails(AbstractContractModel $contract): ?array
    {
        $result = $this->getContractsList([
            $contract->id,
        ]);

        if (empty($result->rows)) {
            return null;
        }

        [$firstContract] = $result->rows;

        return $firstContract;
    }
}
