<?php

declare(strict_types=1);

namespace App\Services\Documents\Contracts;

use App\Types\Enums\Documents\ContractTypeEnum;
use App\Models\UserDb\Documents\Contracts\Contract;
use App\Models\UserDb\Users\UserFarmingPermission;
use App\Services\Helper\QueryHelper;
use App\Types\DTOs\Common\ListDTO;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;

class ContractsService extends AbstractContractsService
{
    public function getContractsList(array $contractIds, ?int $page = 1, ?int $limit = 10, ?array $sort = null): ListDTO
    {
        /** @var \App\Models\Main\User $user */
        $user = Auth::user();
        $allowedFarmingIds = $user->getFarmingIdsByPermissions([UserFarmingPermission::PERMISSION_READ]);
        $ownershipContractType = ContractTypeEnum::Ownership->value;
        $orderBySQL = $this->generateOrderBySQL($sort);

        // Annexes
        $annexesQuery = Contract::selectRaw("
                su_contracts.id,
                su_contracts.c_num as name,
                TO_CHAR(su_contracts.c_date, 'DD.MM.YYYY') as c_date,
                TO_CHAR(su_contracts.start_date, 'DD.MM.YYYY') as start_date,
                TO_CHAR(su_contracts.due_date, 'DD.MM.YYYY') as due_date,
                get_contract_status(su_contracts.id, su_contracts.active, su_contracts.start_date, su_contracts.due_date) as status,
                su_contracts.is_annex,
                false as is_sublease,
                case when su_contracts.from_sublease notnull then true else false end as is_from_sublease,
                su_contracts.parent_id
        ")
            ->whereIn('su_contracts.farming_id', $allowedFarmingIds)
            ->when(
                ! empty($contractIds),
                fn ($q) => $q->whereIn('su_contracts.parent_id', $contractIds)
            );

        // Contracts
        $contractsQuery = Contract::selectRaw("
            ROW_NUMBER() OVER ({$orderBySQL}) as row_num,
            su_contracts.id,
            su_contracts.c_num, -- Contract number
            su_contracts.nm_usage_rights AS c_type, -- Contract type
            TO_CHAR(su_contracts.c_date, 'DD.MM.YYYY') AS c_date,
            TO_CHAR(su_contracts.start_date, 'DD.MM.YYYY') AS start_date,
            TO_CHAR(su_contracts.due_date, 'DD.MM.YYYY') AS due_date,
            su_contracts.active,
            get_contract_status(su_contracts.id, su_contracts.active, su_contracts.start_date, su_contracts.due_date) AS status,
            su_contracts.virtual_contract_type AS type,
            su_contracts.is_sublease,
            su_contracts.from_sublease,
            case when su_contracts.from_sublease notnull then true else false end as is_from_sublease,
            su_contracts.is_annex,
            su_contracts.farming_name,
            su_contracts.overall_renta,
            su_contracts.contract_area,
            su_contracts.renta,
            su_contracts.renta_nat,
            su_contracts.osz_num, -- OSZ contract number 
            TO_CHAR(su_contracts.osz_date, 'DD.MM.YYYY') AS osz_date,-- OSZ contract date
            su_contracts.sv_num, -- Registry Agency contract number
            su_contracts.na_num, 
            su_contracts.tom,
            su_contracts.delo,
            su_contracts.court,
            su_contracts.comment,
            TO_CHAR(su_contracts.sv_date, 'DD.MM.YYYY') AS sv_date, -- Registry Agency date of contract
            COALESCE (
                    JSON_AGG(
                        JSON_BUILD_OBJECT(
                            'file_id', suf.id,
                            'file_name', suf.filename
                        ) 
                        ORDER BY suf.id
                    ) FILTER (WHERE suf.id IS NOT NULL),
                    '[]'::json
            ) AS files,
            COALESCE(
                JSON_AGG(
                    DISTINCT JSON_BUILD_OBJECT(
                    'name', srt.name,
                    'quantity', scr.renta_value,
                    'unit', sum.short_name)::JSONB 
                ) FILTER (WHERE scr.id NOTNULL), 
                '[]'::json
                ) AS renta_nat,
            COALESCE(
                JSON_AGG(
                    JSON_BUILD_OBJECT(
                        'id', COALESCE(annexes.id, parent.id),
                        'name', COALESCE(annexes.name, parent.c_num),
                        'c_date', COALESCE(annexes.c_date, TO_CHAR(parent.c_date, 'DD.MM.YYYY')),
                        'start_date', COALESCE(annexes.start_date, TO_CHAR(parent.start_date, 'DD.MM.YYYY')),
                        'due_date', COALESCE(annexes.due_date, TO_CHAR(parent.due_date,'DD.MM.YYYY')),
                        'status', COALESCE(annexes.status, get_contract_status(parent.id, parent.active, parent.start_date, parent.due_date)),
                        'c_type', parent.nm_usage_rights,
                        'type', parent.virtual_contract_type,
                        'is_annex', COALESCE(annexes.is_annex, parent.is_annex, false),
                        'is_sublease', COALESCE(annexes.is_sublease, parent.is_sublease, false),
                        'is_from_sublease', COALESCE(annexes.is_from_sublease, case when parent.from_sublease notnull then true else false end, false)
                    )
                ) FILTER (WHERE annexes.id IS NOT NULL OR parent.id IS NOT NULL),
                '[]'::json
            ) AS related_contracts
        ")
            ->leftJoin('su_contracts_files_rel AS scfr', 'scfr.contract_id', '=', 'su_contracts.id')
            ->leftJoin('su_user_files AS suf', 'suf.id', '=', 'scfr.file_id')
            ->leftJoin('su_contracts_rents AS scr', 'scr.contract_id', '=', 'su_contracts.id')
            ->leftJoin('su_renta_types AS srt', 'srt.id', '=', 'scr.renta_id')
            ->leftJoin('su_units_of_measure AS sum', 'sum.id', '=', 'srt.unit')
            ->leftJoin('annexes', 'annexes.parent_id', '=', 'su_contracts.id')
            ->leftJoin(
                'su_contracts AS parent',
                fn ($q) => $q->on('parent.id', '=', 'su_contracts.parent_id')
                    ->whereIn('parent.farming_id', $allowedFarmingIds)
            )
            ->when(
                ! empty($contractIds),
                fn ($q) => $q->whereIn('su_contracts.id', $contractIds)
            )
            ->where('su_contracts.is_sublease', false)
            ->when(
                isset($page) && isset($limit),
                fn ($q) => $q->limit($limit)->offset(($page - 1) * $limit)
            )
            ->groupBy('su_contracts.id', 'parent.id');

        // Contractors
        $contractorsQuery = DB::table('contracts AS c')
            ->selectRaw("
                so.id AS owner_id,
                so.is_dead,
                spor.is_heritor,
                COALESCE(ARRAY_LENGTH(ARRAY_AGG(DISTINCT sh.path) FILTER(WHERE sh.path NOTNULL), 1) > 0, false) AS has_heritors,
                scpr.contract_id,
                COALESCE(NULLIF(TRIM(CONCAT_WS(' ', so.name, so.surname, so.lastname)), ''), so.company_name) AS name,
                subpath(spor.path, 0, 1)::text::int AS greatest_grandparent, -- The root owner in the onwers tree
                NULLIF(CONCAT_WS(' ', so2.name, so2.surname, so2.lastname), '') AS greatest_grandparent_name,
                COALESCE(spor.path, so.id::text::ltree) AS path,
                CONCAT_WS(' ', sor.rep_name, sor.rep_surname, sor.rep_lastname) AS rep_name,
                spor.notary_name,
                spor.notary_number,
                spor.notary_address,
                spor.proxy_num,
	            TO_CHAR(spor.proxy_date, 'DD.MM.YYYY') as proxy_date,
                spor.is_signer NOTNULL AS is_signer,
                scpr.id as scpr_id,
                scpr.annex_action,
                (scpr.contract_area * spor.percent / 100)::numeric(8, 4) AS own_area -- The aggreagated area from all plots of this owner
            ")
            ->join('su_contracts_plots_rel AS scpr', 'scpr.contract_id', '=', 'c.id')
            ->join('su_plots_owners_rel AS spor', 'spor.pc_rel_id', '=', 'scpr.id')
            ->join('su_owners AS so', 'so.id', '=', 'spor.owner_id')
            ->leftJoin('su_owners_reps AS sor', 'sor.id', '=', 'spor.rep_id')
            ->leftJoin('su_heritors AS sh', DB::raw('so.id::text::ltree'), '@>', 'sh.path')
            ->leftJoin('su_owners AS so2', 'so2.id', '=', DB::raw('subpath(spor.path, 0, 1)::text::int'))
            ->groupBy(
                'so.id',
                'so.is_dead',
                'spor.id',
                'scpr.id',
                'so2.id',
                'sor.id'
            );

        // contractors tree
        $contractorsTreeQuery = DB::table('contractors AS o')
            ->selectRaw("
                o.contract_id,
                get_descendants(
                    ARRAY_AGG(DISTINCT 
                        (o.owner_id, 
                        o.is_dead, 
                        o.is_heritor, 
                        o.has_heritors,
                        o.name, 
                        o.greatest_grandparent, 
                        o.greatest_grandparent_name, 
                        o.path, o.rep_name)::owner), ''::ltree, 0
                ) AS contractors_tree
            ")
            ->groupBy('o.contract_id');

        // Contractors list
        $contractorsListQuery = DB::table('contractors AS c')
            ->selectRaw('
                c.contract_id,
                c.owner_id as id,
                c.name,
                c.is_dead,
                c.has_heritors,
                SUM(c.own_area) AS area,
                c.greatest_grandparent,
                c.greatest_grandparent_name,
                c.rep_name,
                c.notary_name,
                c.notary_number,
                c.notary_address,
                c.proxy_num,
                c.proxy_date,
                BOOL_OR(c.is_signer) AS is_signer,
                c.is_heritor,
                BOOL_AND(c.annex_action = \'removed\') AS all_plots_removed -- If all plots are removed with annex
            ')
            ->where(function (Builder $query) {
                $query->where('c.is_dead', false)
                    ->orWhere(function (Builder $query) {
                        $query->where('c.is_dead', true)
                            ->where('c.has_heritors', false)
                            ->where('c.own_area', '>', 0);
                    });
            })
            ->groupBy(
                'c.owner_id',
                'c.name',
                'c.is_dead',
                'c.has_heritors',
                'c.rep_name',
                'c.contract_id',
                'c.notary_name',
                'c.notary_number',
                'c.notary_address',
                'c.proxy_num',
                'c.proxy_date',
                'c.is_heritor',
                'c.greatest_grandparent',
                'c.greatest_grandparent_name'
            )
            ->orderBy('c.is_dead')
            ->orderBy('c.name')
            ->orderBy('c.contract_id');

        // Contractors list in json format
        $contractorsListJsonQuery = DB::table('contractors_list AS ol')
            ->selectRaw('
                ol.contract_id,
                JSONB_AGG(ol) AS contractors_json,
                SUM(ol.area) AS contractors_area
            ')
            ->groupBy('ol.contract_id');

        // Plots list
        $plotsListQuery = DB::table('contracts AS c')
            ->selectRaw("
            scpr.contract_id
            , plot_id AS id
            , kad_ident
            , lk.virtual_ekatte_name AS ekatte_name
            , lk.virtual_ntp_title AS ntp_title
            -- temporary trim the category title to get some space for the other columns in FE documents drawer
            , TRIM(LEADING 'Категория ' FROM lk.virtual_category_title) AS category_title
            , COALESCE(scpr.area_for_rent, 0) AS area_for_rent
            , scpr.rent_per_plot
            , lk.allowable_area
            , scpr.contract_area
            , scpr.annex_action
            , CASE WHEN lk.is_edited = true AND lk.edit_active_from < now() THEN true
                   ELSE false
              END AS is_archive
            , lk.document_area 
            , scpr.comment,
            CASE 
                WHEN c.c_type = {$ownershipContractType} then false
                WHEN sum(ROUND(spor.\"percent\"::numeric,2)) <= 99 THEN true 
                WHEN count(so.owner_id) = 0 THEN true
                ELSE false 
            END AS is_partial_ownership
            , COALESCE(
                JSONB_AGG(distinct JSONB_BUILD_OBJECT(
                    'id' , so.owner_id ,
                    'name' , so.name , 
                    'percent' , ROUND(spor.\"percent\"::numeric,2) , 
                    'fraction', CASE
                            WHEN spor.numerator notnull AND spor.denominator notnull THEN CONCAT(spor.numerator, '/' , spor.denominator)
                            ELSE percent_to_fraction(spor.\"percent\")
                        END,
                    'is_dead', so.is_dead, 
                    'is_heritor', spor.is_heritor,
                    'greatest_grandparent', so.greatest_grandparent,
                    'greatest_grandparent_name', so.greatest_grandparent_name,
                    'area' , so.own_area)
                ) FILTER (WHERE so.owner_id NOTNULL), '[]'::jsonb)
                AS contractors
        ")
            ->join('su_contracts_plots_rel AS scpr', 'scpr.contract_id', '=', 'c.id')
            ->join('layer_kvs AS lk', 'lk.gid', '=', 'plot_id')
            ->leftJoin('su_plots_owners_rel AS spor', 'spor.pc_rel_id', '=', 'scpr.id')
            ->leftJoin('contractors as so', function (Builder $join) {
                $join->on('so.owner_id', '=', 'spor.owner_id')
                    ->on('so.scpr_id', '=', 'scpr.id')
                    ->where('so.is_dead', '=', false)
                    ->where(function (Builder $query) {
                        $query->on('so.path', '=', 'spor.path')
                            ->orWhere(function (Builder $query) {
                                // In the Owner CTE, we use 'COALESCE(spor.path, so.id::text::ltree) AS path'.
                                // Therefore, 'so.path' is always not null.
                                // However, if 'spor.path' is null, it means the owner is not a heritor.
                                // In this case, we should check if 'so.path' equals 'so.owner_id::text::ltree'.
                                $query->whereNull('spor.path')
                                    ->whereRaw('so.owner_id::text::ltree = so.path');
                            });
                    });
            })
            ->where('scpr.annex_action', '<>', 'removed')
            ->groupBy(
                'plot_id',
                'kad_ident',
                'ekatte_name',
                'ntp_title',
                'category_title',
                'scpr.comment',
                'area_for_rent',
                'scpr.contract_area',
                'rent_per_plot',
                'lk.allowable_area',
                'scpr.contract_id',
                'lk.document_area',
                'scpr.annex_action',
                'lk.is_edited',
                'lk.edit_active_from',
                'c.c_type'
            );

        // Plots list json
        $plotsListJsonQuery = DB::table('plots_list AS pl')
            ->selectRaw('
                pl.contract_id
                , JSONB_AGG(ROW_TO_JSON(pl)) AS plots_json
                , ARRAY_AGG(DISTINCT pl.ekatte_name) AS ekatte_names
                , SUM(pl.document_area) as document_area
                , SUM(pl.area_for_rent) as area_for_rent
                , SUM(pl.allowable_area) as allowable_area
            ')
            ->groupBy('pl.contract_id');

        $resultQuery = DB::table('contracts AS c')
            ->selectRaw("
                JSONB_BUILD_OBJECT(
                    'details', row_to_json(c)::JSONB || JSONB_BUILD_OBJECT(
                        'ekatte_names', plj.ekatte_names,
                        'contract_area', COALESCE(c.contract_area, 0)
                    ),
                    'contractors', oj.contractors_json,
                    'contractors_tree', ot.contractors_tree,
                    'plots', JSONB_BUILD_OBJECT(
                        'rows', plj.plots_json,
                        'footer', JSONB_BUILD_ARRAY(
                            JSONB_BUILD_OBJECT(
                                'title', 'by contract',
                                'value', COALESCE(c.contract_area, 0)
                            ),
                            JSONB_BUILD_OBJECT(
                                'title', 'for rent',
                                'value', COALESCE(plj.area_for_rent, 0)
                            ),
                            JSONB_BUILD_OBJECT(
                                'title', 'allowable',
                                'value', COALESCE(plj.allowable_area, 0)
                            ),
                            JSONB_BUILD_OBJECT(
                                'title', 'by document',
                                'value', COALESCE(plj.document_area, 0)
                            )
                        )
                    )
                ) AS row
            ")
            ->leftJoin('contractors_list_json AS oj', 'oj.contract_id', '=', 'c.id')
            ->leftJoin('contractors_tree AS ot', 'ot.contract_id', '=', 'c.id')
            ->leftJoin('plots_list_json AS plj', 'plj.contract_id', '=', 'c.id')
            ->orderBy('c.row_num', 'asc');

        $rows = DB::table('result')
            ->withExpression('annexes', $annexesQuery)
            ->withExpression('contracts', $contractsQuery)
            ->withExpression('contractors', $contractorsQuery)
            ->withExpression('contractors_tree', $contractorsTreeQuery)
            ->withExpression('contractors_list', $contractorsListQuery)
            ->withExpression('contractors_list_json', $contractorsListJsonQuery)
            ->withExpression('plots_list', $plotsListQuery)
            ->withExpression('plots_list_json', $plotsListJsonQuery)
            ->withExpression('result', $resultQuery)
            ->selectRaw('
                result.row
            ')
            ->get()
            ->pluck('row')
            ->map(fn ($row) => json_decode($row, true))
            ->toArray();


        $total = DB::table('su_contracts AS c')
            ->selectRaw('COUNT(DISTINCT c.id) AS total')
            ->when(
                ! empty($contractIds),
                fn ($q) => $q->whereIn('c.id', $contractIds)
            )
            ->where('c.is_sublease', false)
            ->first()->total ?? 0;

        return  new ListDTO([
            'rows' => $rows,
            'total' => $total,
        ]);
    }

    public function getContractsListFooter(array $contractIds): array
    {
        $contrtractPlotsQuery = DB::table('su_contracts', 'c')
            ->select(
                'scpr.id',
                'scpr.plot_id',
                'scpr.area_for_rent',
                'scpr.contract_area'
            )
            ->leftJoin(
                'su_contracts as a',
                fn ($join) => $join
                    ->on('a.id', '=', 'c.parent_id')
                    ->where('a.is_sublease', false)
                    ->when(
                        ! empty($contractIds),
                        fn ($q) => $q->whereIn('c.id', $contractIds)
                    )
            )
            ->join('su_contracts_plots_rel AS scpr', 'scpr.contract_id', '=', DB::raw('COALESCE(a.id, c.id)'))
            ->when(
                ! empty($contractIds),
                fn ($q) => $q->whereIn('c.id', $contractIds)
            )
            ->where('c.is_sublease', false)
            ->where('scpr.annex_action', '<>', 'removed')
            ->whereIn(
                DB::raw('get_contract_status(c.id, c.active, c.start_date, c.due_date)::TEXT'),
                ['Active', 'Annexed']
            );

        $areasByPlotQuery = DB::table('contract_plots', 'cp')
            ->withExpression('contract_plots', $contrtractPlotsQuery)
            ->select(
                DB::raw('SUM(COALESCE(cp.area_for_rent, 0)) AS total_area_for_rent'),
                DB::raw('SUM(COALESCE(lk.allowable_area, 0)) AS total_allowable_area'),
                DB::raw('SUM(COALESCE(lk.document_area, 0)) AS total_document_area'),
            )
            ->leftJoin('layer_kvs AS lk', 'lk.gid', '=', 'plot_id')
            ->first();


        $areasByOwnerQuery = DB::table('contract_plots', 'cp')
            ->withExpression('contract_plots', $contrtractPlotsQuery)
            ->select(
                DB::raw('SUM((COALESCE(cp.contract_area, 0) * COALESCE(spor.percent, 0) / 100)::numeric(8, 4)) AS total_contract_area')
            )
            ->join(
                'su_plots_owners_rel AS spor',
                fn ($join) => $join
                    ->on('spor.pc_rel_id', '=', 'cp.id')
                    ->where('spor.is_heritor', false)
            )
            ->first();

        return [...(array) $areasByPlotQuery, ...(array) $areasByOwnerQuery];
    }

    private function generateOrderBySQL(?array $sort): string
    {
        $columnMapFn = fn ($sortKey) => match($sortKey) {
            'id' => 'su_contracts.id',
            'c_num' => 'su_contracts.c_num',
            'start_date' => 'su_contracts.start_date',
            'farming_name' => 'su_contracts.farming_name',
            'contract_area' => 'su_contracts.contract_area',
            'type' => 'su_contracts.virtual_contract_type',
            'due_date' => 'su_contracts.due_date',
            'status' => 'su_contracts.virtual_contract_status',
            'comment' => 'su_contracts.comment',
            default => throw new InvalidArgumentException("Invalid sort column: {$sortKey}"),
        };


        $sort = array_map(
            fn ($sortOptions) => [
                'column' => $columnMapFn($sortOptions['column']),
                'direction' => $sortOptions['direction'],
            ],
            $sort ?? []
        );

        return QueryHelper::generateOrderBySQL($sort);
    }
}
