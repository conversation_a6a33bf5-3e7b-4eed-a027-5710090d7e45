<?php

declare(strict_types=1);

namespace App\Services\Helper;

use Illuminate\Database\Query\Builder;

class QueryHelper
{
    /**
     * Remove the joins from the $fromQuery and add them to the $toQuery.
     *
     * Example:
     * When having query like: $mainQ->where(fn ($subQ) => $subQ->join(...)->on(..)->where(..)),
     * the $subQ is a new instance of Builder, so the $mainQ does not inherit the joins from $subQ.
     * This method can be used to transfer the joins from $subQ to $mainQ.
     */
    public static function transferJoins(Builder $fromQuery, Builder $toQuery)
    {
        $transferredJoins = collect($toQuery->joins ?? [])->pluck('table')->toArray();

        foreach ($fromQuery->joins ?? [] as $joinToTransfer) {
            if (! in_array($joinToTransfer->table, $transferredJoins)) {
                $toQuery->joins[] = $joinToTransfer;
                $transferredJoins[] = $joinToTransfer->table;
            }
        }

        $fromQuery->joins = [];
    }

    /**
     * Generate an ORDER BY SQL expression from the provided sort options.
     *
     * @param array $options an array of items, each containing 'column' and 'direction'
     *
     * @return null|string returns the ORDER BY SQL expression or empty string if no valid sort options are provided
     */
    public static function generateOrderBySQL(?array $options): string
    {
        $options = collect($options ?? [])
            ->filter(
                fn ($option) => isset($option['column'], $option['direction'])
                    && in_array(strtolower($option['direction']), ['asc', 'desc'], true)
            );

        if ($options->isEmpty()) {
            return '';
        }

        return 'ORDER BY ' . $options
            ->reduce(
                function ($carry, $option) {
                    $direction = strtolower($option['direction']);
                    $column = $option['column'];

                    if (strlen($carry) > 0) {
                        return "{$carry}, {$column} {$direction}";
                    }

                    return "{$column} {$direction}";
                },
                ''
            );
    }
}
