<?php

declare(strict_types=1);

namespace App\Services\ContractPlotFilter;

use App\Pipes\ContractPlotFilter\Contract\BaseContractFilterPipe;
use App\Pipes\ContractPlotFilter\Contract\CnumFilterPipe;
use App\Pipes\ContractPlotFilter\Contract\ContractClosedForEditingFilterPipe;
use App\Pipes\ContractPlotFilter\Contract\ContractCommentFilterPipe;
use App\Pipes\ContractPlotFilter\Contract\ContractDateFromFilterPipe;
use App\Pipes\ContractPlotFilter\Contract\ContractDateToFilterPipe;
use App\Pipes\ContractPlotFilter\Contract\ContractGroupFilterPipe;
use App\Pipes\ContractPlotFilter\Contract\ContractStatusTextFilterPipe;
use App\Pipes\ContractPlotFilter\Contract\ContractTypeFilterPipe;
use App\Pipes\ContractPlotFilter\Contract\FarmingNameFilterPipe;
use App\Pipes\ContractPlotFilter\Contract\FarmingYearsFilterPipe;
use App\Pipes\ContractPlotFilter\Contract\NotaryNumberFilterPipe;
use App\Pipes\ContractPlotFilter\Contract\UserFarmingPermissionFilterPipe;
use App\Pipes\Query\QueryFilterGroupPipe;
use App\Pipes\ContractPlotFilter\FarmingYear\ActiveContractPlotsByFarmingYearFilterPipe;
use App\Pipes\ContractPlotFilter\FarmingYear\ExpiringContractPlotsByFarmingYearFilterPipe;
use App\Pipes\ContractPlotFilter\FarmingYear\ForSubleaseContractPlotsByFarmingYearFilterPipe;
use App\Pipes\ContractPlotFilter\Owner\BaseOwnerFilterPipe;
use App\Pipes\ContractPlotFilter\Owner\IncompleteOwnershipDetailsFilterPipe;
use App\Pipes\ContractPlotFilter\Owner\CompanyEikFilterPipe;
use App\Pipes\ContractPlotFilter\Owner\CompanyNameFilterPipe;
use App\Pipes\ContractPlotFilter\Owner\ContractSignerFilterPipe;
use App\Pipes\ContractPlotFilter\Owner\ContragentCommentFilterPipe;
use App\Pipes\ContractPlotFilter\Owner\ContragentFilterPipe;
use App\Pipes\ContractPlotFilter\Owner\ContragentPhoneFilterPipe;
use App\Pipes\ContractPlotFilter\Owner\ContragentTypeFilterPipe;
use App\Pipes\ContractPlotFilter\Owner\HeritorEgnFilterPipe;
use App\Pipes\ContractPlotFilter\Owner\HeritorNameFilterPipe;
use App\Pipes\ContractPlotFilter\Owner\OwnerEgnFilterPipe;
use App\Pipes\ContractPlotFilter\Owner\OwnerMultiFieldFilterPipe;
use App\Pipes\ContractPlotFilter\Plot\AllowableTypeFilterPipe;
use App\Pipes\ContractPlotFilter\Plot\BlockFilterPipe;
use App\Pipes\ContractPlotFilter\Plot\CommentFilterPipe;
use App\Pipes\ContractPlotFilter\Plot\VirtualEkatteNameFilterPipe;
use App\Pipes\ContractPlotFilter\Plot\IrrigatedAreaFilterPipe;
use App\Pipes\ContractPlotFilter\Plot\KadIdentFilterPipe;
use App\Pipes\ContractPlotFilter\Plot\MasivFilterPipe;
use App\Pipes\ContractPlotFilter\Plot\MestnostFilterPipe;
use App\Pipes\ContractPlotFilter\Plot\NumberFilterPipe;
use App\Pipes\ContractPlotFilter\Owner\OwnerNameFilterPipe;
use App\Pipes\ContractPlotFilter\Owner\OwnerOSZEgnFilterPipe;
use App\Pipes\ContractPlotFilter\Owner\OwnerOSZNameFilterPipe;
use App\Pipes\ContractPlotFilter\Owner\PersonEgnFilterPipe;
use App\Pipes\ContractPlotFilter\Owner\PersonNameFilterPipe;
use App\Pipes\ContractPlotFilter\Owner\RepresentativeEgnFilterPipe;
use App\Pipes\ContractPlotFilter\Owner\RepresentativeNameFilterPipe;
use App\Pipes\ContractPlotFilter\Plot\ParticipationFilterPipe;
use App\Pipes\ContractPlotFilter\Plot\PlotIdFilterPipe;
use App\Pipes\ContractPlotFilter\Plot\VirtualCategoryTitleFilterPipe;
use App\Pipes\ContractPlotFilter\Plot\VirtualContractTypeFilterPipe;
use App\Pipes\ContractPlotFilter\Plot\VirtualNtpTitleFilterPipe;
use App\Pipes\ContractPlotFilter\Rent\RentKindTypeFilterPipe;
use App\Pipes\ContractPlotFilter\Rent\RentTypeFilterPipe;
use App\Pipes\ContractPlotFilter\Rent\SpecificRentTypeFilterPipe;
use App\Types\DTOs\Common\ListDTO;
use App\Types\Interfaces\ContractPlot\ContractPlotFilterPipe;
use App\Types\Interfaces\Query\QueryFilterPipe;
use Illuminate\Pipeline\Pipeline;
use Exception;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;

class ContractPlotFilter
{
    private Builder $query;

    public function __construct(private string $baseTable)
    {
        if (empty($baseTable)) {
            throw new Exception('Invalid base table');
        }

        $this->query = DB::table($baseTable);
    }

    /**
     * This method is used to apply filters to the query.
     *
     * @return $this
     */
    public function withParams(array $filterParams): self
    {
        $userFarmingPermissionsFilter = new UserFarmingPermissionFilterPipe(Auth::user()->getAuthIdentifier());
        $hasFilterByFarmingPermissions = false;

        // Create filters for $filterParams['contract_types']

        /**
         * @var QueryFilterGroupPipe $contractTypesGroupFilter Pipe containing filter by $filterParams['contract_types']
         *                           and filter by farming permissions (needed when filtering by contract properties)
         */
        $contractTypesGroupFilter = new QueryFilterGroupPipe(QueryFilterPipe::OPERATION_AND);
        if (isset($filterParams['contract_types'])) {
            $contractTypesFilter = $this->getFilter('contract_types', $filterParams['contract_types']);
            $contractTypesGroupFilter->add([$contractTypesFilter, $userFarmingPermissionsFilter]);
            $hasFilterByFarmingPermissions = true;
        }

        // Create filters for $filterParams['groups']

        /**
         * @var QueryFilterGroupPipe $groups Pipe containing the filters for $filterParams['groups']
         */
        $groups = new QueryFilterGroupPipe(QueryFilterPipe::OPERATION_OR);

        foreach (($filterParams['groups'] ?? []) as $groupParams) {
            /**
             * @var QueryFilterGroupPipe $group Pipe containing the filters for one item of $filterParams['groups']
             */
            $group = new QueryFilterGroupPipe(QueryFilterPipe::OPERATION_AND);

            foreach ($groupParams as $filterKey => $filterValues) {
                // Add filter to the group
                $filter = $this->getFilter($filterKey, $filterValues);
                $group->add($filter);
            }

            // Add filter by farming permissions to the group if it is not already present and the group contains filters that require it
            if (! $hasFilterByFarmingPermissions) {
                $groupContainsFiltersRequiringFarmingPermissions = $group->getFilters()->some(
                    fn ($pipe) => $this->requireFilterByFarmingPermission($pipe)
                );

                if ($groupContainsFiltersRequiringFarmingPermissions) {
                    // Add farming permission filter to the group
                    $group->add($userFarmingPermissionsFilter);
                }
            }

            // Add group to the groups filter
            $groups->add($group);
        }

        // Combine filters

        /**
         * @var QueryFilterGroupPipe $allFilters Pipe containing the filters for $filterParams['groups'] and $filterParams['contract_types']
         */
        $allFilters = new QueryFilterGroupPipe(
            QueryFilterPipe::OPERATION_AND,
            [
                $groups,
                $contractTypesGroupFilter,
            ]
        );

        // Apply the filters to the query
        $this->query = app(Pipeline::class)
            ->send($this->query)
            ->through($allFilters)
            ->thenReturn();

        return $this;
    }

    public function getQuery(): Builder
    {
        return $this->query;
    }

    public function getFilterItems(string $filterKey, ?string $search, ?int $page, ?int $limit, ?string $sort = "item->>'value'", ?string $order = 'asc'): ListDTO
    {
        // Get the filter whoose items we we want to get
        $filter = $this->getFilter($filterKey);

        // Execute the handleFilterItems which returns the query for the filter items
        $filterItemsQuery = app(Pipeline::class)
            ->send($this->query)
            ->through($filter)
            ->via('handleFilterItems')
            ->thenReturn();

        $filterItemsJsonQuery = DB::table('filter_items')
            ->selectRaw('ROW_TO_JSON(filter_items)::JSONB AS item');

        $searchedItemsQuery = DB::table('filter_items_json')
            ->selectRaw("
                JSONB_BUILD_OBJECT(
                    'label', item->'label',
                    'value', item->'value'
                ) 
                || 
                CASE WHEN (item->'has_delimiter')::BOOL = TRUE
                    THEN JSONB_BUILD_OBJECT('has_delimiter', TRUE)
                    ELSE '{}'::JSONB 
                END AS item
            ") // ensure that only label, value and has_delimiter are returned
            ->when(
                $search,
                fn ($q) => $q->whereRaw("item->>'value' ILIKE '%{$search}%'")
            )
            ->orderBy(DB::raw("item->>'sort_priority'"), 'asc') // First order by sort_priority if returned by the query
            ->when(
                $sort && $order,
                fn ($q) => $q->orderBy(DB::raw($sort), $order)
            );


        $paginatedItemsQuery = DB::table('searched_items')
            ->selectRaw('searched_items.item')
            ->when(
                $page && $limit,
                fn ($q) => $q->limit($limit)->offset(($page - 1) * $limit)
            );

        $rowsQuery = DB::table('paginated_items')
            ->selectRaw('JSONB_AGG(item) AS data');

        $totalQuery = DB::table('searched_items')
            ->selectRaw('COUNT(*) AS data');

        $result = DB::table('rows')
            ->withExpression('filter_items', $filterItemsQuery)
            ->withExpression('filter_items_json', $filterItemsJsonQuery)
            ->withExpression('searched_items', $searchedItemsQuery)
            ->withExpression('paginated_items', $paginatedItemsQuery)
            ->withExpression('rows', $rowsQuery)
            ->withExpression('total', $totalQuery)
            ->crossJoin('total')
            ->selectRaw("
                    JSONB_BUILD_OBJECT(
                        'rows', COALESCE(rows.data, '[]'::JSONB),
                        'total', COALESCE(total.data, 0)
                    ) AS data
                ")
            ->first();

        $result = json_decode($result->data, true);

        return new ListDTO($result);
    }

    private function getFilter(string $filterKey, $filterValues = null): ContractPlotFilterPipe
    {
        return match($filterKey) {
            // Plot filters
            'kad_ident' => new KadIdentFilterPipe($filterValues),
            'virtual_ekatte_name' => new VirtualEkatteNameFilterPipe($filterValues),
            'masiv' => new MasivFilterPipe($filterValues),
            'number' => new NumberFilterPipe($filterValues),
            'virtual_category_title' => new VirtualCategoryTitleFilterPipe($filterValues),
            'virtual_ntp_title' => new VirtualNtpTitleFilterPipe($filterValues),
            'mestnost' => new MestnostFilterPipe($filterValues),
            'block' => new BlockFilterPipe($filterValues),
            'allowable_type' => new AllowableTypeFilterPipe($filterValues),
            'irrigated_area' => new IrrigatedAreaFilterPipe($filterValues),
            'participation' => new ParticipationFilterPipe($filterValues),
            'comment' => new CommentFilterPipe($filterValues),
            'plot_id' => new PlotIdFilterPipe($filterValues),
            'virtual_contract_type' => new VirtualContractTypeFilterPipe($filterValues),

            // Contract filters
            'cnum' => new CnumFilterPipe($filterValues),
            'group' => new ContractGroupFilterPipe($filterValues),
            'notary_number' => new NotaryNumberFilterPipe($filterValues),
            'incomplete_ownership_details' => new IncompleteOwnershipDetailsFilterPipe($filterValues),
            'contract_comment' => new ContractCommentFilterPipe($filterValues),
            'closed_for_editing' => new ContractClosedForEditingFilterPipe($filterValues),
            'contract_date_from' => new ContractDateFromFilterPipe($filterValues),
            'contract_date_to' => new ContractDateToFilterPipe($filterValues),
            'contract_status_text' => new ContractStatusTextFilterPipe($filterValues),
            'farming_name' => new FarmingNameFilterPipe($filterValues),
            'farming_years' => new FarmingYearsFilterPipe($filterValues),
            'contract_types' => new ContractTypeFilterPipe($filterValues),

            // Rent filters
            'rent_type' => new RentTypeFilterPipe($filterValues),
            'specific_rent_type' => new SpecificRentTypeFilterPipe($filterValues),
            'rent_kind_type' => new RentKindTypeFilterPipe($filterValues),

            // Оwner filters
            'contragent' => new ContragentFilterPipe($filterValues),
            'contragent_type' => new ContragentTypeFilterPipe($filterValues),
            'contragent_phone' => new ContragentPhoneFilterPipe($filterValues),
            'contragent_comment' => new ContragentCommentFilterPipe($filterValues),
            'contract_signer' => new ContractSignerFilterPipe($filterValues),
            'owner_name' => new OwnerNameFilterPipe($filterValues),
            'owner_egn' => new OwnerEgnFilterPipe($filterValues),
            'company_name' => new CompanyNameFilterPipe($filterValues),
            'company_eik' => new CompanyEikFilterPipe($filterValues),
            'heritor_name' => new HeritorNameFilterPipe($filterValues),
            'heritor_egn' => new HeritorEgnFilterPipe($filterValues),
            'rep_egn' => new RepresentativeEgnFilterPipe($filterValues),
            'rep_name' => new RepresentativeNameFilterPipe($filterValues),
            'person_name' => new PersonNameFilterPipe($filterValues),
            'person_egn' => new PersonEgnFilterPipe($filterValues),
            'egn_subekt' => new OwnerOSZEgnFilterPipe($filterValues),
            'osz_ime_subekt' => new OwnerOSZNameFilterPipe($filterValues),
            'owner_multi_field_search' => new OwnerMultiFieldFilterPipe($filterValues),

            // Farming year filters
            'farm_year_active_contract_plots' => new ActiveContractPlotsByFarmingYearFilterPipe($filterValues),
            'expiring_contracts_for_farm_year' => new ExpiringContractPlotsByFarmingYearFilterPipe($filterValues),
            'for_sublease_farm_years' => new ForSubleaseContractPlotsByFarmingYearFilterPipe($filterValues),

            default => throw new InvalidArgumentException("Unknown filter key: {$filterKey}"),
        };
    }

    /**
     * Check if the filter requires farming permissions.
     *
     * @return bool
     */
    private function requireFilterByFarmingPermission(QueryFilterPipe $pipe)
    {
        return
            $pipe instanceof BaseContractFilterPipe
            || $pipe instanceof BaseOwnerFilterPipe
            || $pipe instanceof ActiveContractPlotsByFarmingYearFilterPipe
            || $pipe instanceof ExpiringContractPlotsByFarmingYearFilterPipe
            || $pipe instanceof ForSubleaseContractPlotsByFarmingYearFilterPipe
            || $pipe instanceof OwnerMultiFieldFilterPipe;

    }
}
