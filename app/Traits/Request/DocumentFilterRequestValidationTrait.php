<?php

declare(strict_types=1);

namespace App\Traits\Request;

use App\Types\Enums\Documents\ContractTypeEnum;
use Illuminate\Validation\Rule;

trait DocumentFilterRequestValidationTrait
{
    public function validationRules(): array
    {
        return [
            'filter_params' => ['required', 'array'],

            // Contract types validation
            'filter_params.contract_types' => ['array'],
            'filter_params.contract_types.*' => ['nullable', Rule::in(ContractTypeEnum::values())],

            // Filter groups validation
            'filter_params.groups' => ['array'],

            // Plot filters validation
            'filter_params.groups.*.kad_ident' => ['array'],
            'filter_params.groups.*.kad_ident.*' => ['string', 'nullable'],

            'filter_params.groups.*.virtual_ekatte_name' => ['array'],
            'filter_params.groups.*.virtual_ekatte_name.*' => ['string', 'nullable'],

            'filter_params.groups.*.masiv' => ['array'],
            'filter_params.groups.*.masiv.*' => ['string', 'nullable'],

            'filter_params.groups.*.number' => ['array'],
            'filter_params.groups.*.number.*' => ['string', 'nullable'],

            'filter_params.groups.*.virtual_category_title' => ['array'],
            'filter_params.groups.*.virtual_category_title.*' => ['string', 'nullable'],

            'filter_params.groups.*.virtual_ntp_title' => ['array'],
            'filter_params.groups.*.virtual_ntp_title.*' => ['string', 'nullable'],

            'filter_params.groups.*.mestnost' => ['array'],
            'filter_params.groups.*.mestnost.*' => ['string', 'nullable'],

            'filter_params.groups.*.block' => ['array'],
            'filter_params.groups.*.block.*' => ['string', 'nullable'],

            'filter_params.groups.*.allowable_type' => ['array'],
            'filter_params.groups.*.allowable_type.*' => ['string', 'nullable'],

            'filter_params.groups.*.irrigated_area' => ['array'],
            'filter_params.groups.*.irrigated_area.*' => ['string', 'nullable'],

            'filter_params.groups.*.participation' => ['array'],
            'filter_params.groups.*.participation.*' => ['string', 'nullable'],

            'filter_params.groups.*.comment' => ['array'],
            'filter_params.groups.*.comment.*' => ['string', 'nullable'],

            'filter_params.groups.*.plot_id' => ['array'],
            'filter_params.groups.*.plot_id.*' => ['string', 'nullable'],

            'filter_params.groups.*.virtual_contract_type' => ['array'],
            'filter_params.groups.*.virtual_contract_type.*' => ['string', 'nullable'],

            // Contract filters validation
            'filter_params.groups.*.cnum' => ['array'],
            'filter_params.groups.*.cnum.*' => ['string', 'nullable'],

            'filter_params.groups.*.contract_date_from' => ['date_format:Y-m-d', 'nullable'],

            'filter_params.groups.*.contract_date_to' => ['date_format:Y-m-d', 'nullable'],

            'filter_params.groups.*.incomplete_ownership_details' => ['boolean', 'nullable'],

            'filter_params.groups.*.group' => ['array'],
            'filter_params.groups.*.group.*' => ['string', 'nullable'],

            'filter_params.groups.*.closed_for_editing' => ['boolean'],

            'filter_params.groups.*.notary_number' => ['array'],
            'filter_params.groups.*.notary_number.*' => ['string', 'nullable'],

            'filter_params.groups.*.contract_comment' => ['array'],
            'filter_params.groups.*.contract_comment.*' => ['string', 'nullable'],

            'filter_params.groups.*.contract_status_text' => ['array'],
            'filter_params.groups.*.contract_status_text.*' => ['string', 'nullable'],

            'filter_params.groups.*.farming_name' => ['array'],
            'filter_params.groups.*.farming_name.*' => ['string', 'nullable'],

            'filter_params.groups.*.farming_years' => ['array'],
            'filter_params.groups.*.farming_years.*' => ['string', 'nullable'],

            // Rent
            'filter_params.groups.*.rent_type' => ['array'],
            'filter_params.groups.*.rent_type.*' => ['string', 'nullable'],

            'filter_params.groups.*.specific_rent_type' => ['array'],
            'filter_params.groups.*.specific_rent_type.*' => ['string', 'nullable'],

            'filter_params.groups.*.rent_kind_type' => ['array'],
            'filter_params.groups.*.rent_kind_type.*' => ['string', 'nullable'],

            // Owner filters validation
            'filter_params.groups.*.contragent' => ['array'],
            'filter_params.groups.*.contragent.*' => ['string', 'nullable'],

            'filter_params.groups.*.contragent_type' => ['array'],
            'filter_params.groups.*.contragent_type.*' => ['string', 'nullable'],

            'filter_params.groups.*.contract_signer' => ['array'],
            'filter_params.groups.*.contract_signer.*' => ['string', 'nullable'],

            'filter_params.groups.*.contragent_phone' => ['array'],
            'filter_params.groups.*.contragent_phone.*' => ['string', 'nullable'],

            'filter_params.groups.*.contragent_comment' => ['array'],
            'filter_params.groups.*.contragent_comment.*' => ['string', 'nullable'],

            'filter_params.groups.*.owner_name' => ['array'],
            'filter_params.groups.*.owner_name.*' => ['string', 'nullable'],

            'filter_params.groups.*.owner_egn' => ['array'],
            'filter_params.groups.*.owner_egn.*' => ['string', 'nullable'],

            'filter_params.groups.*.company_name' => ['array'],
            'filter_params.groups.*.company_name.*' => ['string', 'nullable'],

            'filter_params.groups.*.company_eik' => ['array'],
            'filter_params.groups.*.company_eik.*' => ['string', 'nullable'],

            'filter_params.groups.*.heritor_name' => ['array'],
            'filter_params.groups.*.heritor_name.*' => ['string', 'nullable'],

            'filter_params.groups.*.heritor_egn' => ['array'],
            'filter_params.groups.*.heritor_egn.*' => ['string', 'nullable'],

            'filter_params.groups.*.rep_egn' => ['array'],
            'filter_params.groups.*.rep_egn.*' => ['string', 'nullable'],

            'filter_params.groups.*.rep_name' => ['array'],
            'filter_params.groups.*.rep_name.*' => ['string', 'nullable'],

            'filter_params.groups.*.person_name' => ['array'],
            'filter_params.groups.*.person_name.*' => ['string', 'nullable'],

            'filter_params.groups.*.person_egn' => ['array'],
            'filter_params.groups.*.person_egn.*' => ['string', 'nullable'],

            'filter_params.groups.*.egn_subekt' => ['array'],
            'filter_params.groups.*.egn_subekt.*' => ['string', 'nullable'],

            'filter_params.groups.*.osz_ime_subekt' => ['array'],
            'filter_params.groups.*.osz_ime_subekt.*' => ['string', 'nullable'],

            'filter_params.groups.*.owner_multi_field_search' => ['array'],
            'filter_params.groups.*.owner_multi_field_search.*' => ['string', 'nullable'],

            // Farming year filters validation
            'filter_params.groups.*.farm_year_active_contract_plots' => ['string'],
            'filter_params.groups.*.expiring_contracts_for_farm_year' => ['string'],
            'filter_params.groups.*.for_sublease_farm_years' => ['string'],

            // Pagination validation
            'page' => ['integer', 'nullable'],
            'limit' => ['integer', 'nullable'],

            // Sort validation
            'sort' => ['array', 'nullable'],
            'sort.*.column' => ['string'],
            'sort.*.direction' => ['string', 'in:asc,desc'],

        ];
    }
}
