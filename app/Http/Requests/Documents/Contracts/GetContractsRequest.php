<?php

declare(strict_types=1);

namespace App\Http\Requests\Documents\Contracts;

use App\Traits\Request\DocumentFilterRequestValidationTrait;
use App\Types\Enums\Documents\DocumentTypeEnum;
use Illuminate\Foundation\Http\FormRequest;

class GetContractsRequest extends FormRequest
{
    use DocumentFilterRequestValidationTrait;

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return $this->validationRules();
    }

    public function includeFooter(): bool
    {
        $reqParams = $this->validated();
        $filterGroups = collect($reqParams['filter_params']['groups'] ?? []);

        return count($filterGroups) > 0 && $filterGroups->every(function ($group) {
            return 1 === count($group['farming_years'] ?? []) && 1 === count($group['farming_name'] ?? []);
        });
    }

    protected function prepareForValidation()
    {
        // Decode the filter_params JSON string into an associative array
        $filterParamsDecoded = json_decode($this->input('filter_params'), true);

        if (is_array($filterParamsDecoded)) {

            // Add contract types to the filter_params
            $documentType = DocumentTypeEnum::from($this->route('type'));
            $filterParamsDecoded['contract_types'] = $documentType
                ->contractTypes()
                ->map(fn ($contractType) => $contractType->value)
                ->toArray();

            $this->merge(['filter_params' => $filterParamsDecoded]);
        }

        // Decode the sort JSON string into an associative array
        $sortDecoded = json_decode($this->input('sort'), true);
        if (is_array($sortDecoded)) {
            $this->merge(['sort' => $sortDecoded]);
        }

    }
}
