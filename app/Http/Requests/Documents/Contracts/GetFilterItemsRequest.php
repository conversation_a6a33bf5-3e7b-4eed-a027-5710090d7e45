<?php

declare(strict_types=1);

namespace App\Http\Requests\Documents\Contracts;

use App\Traits\Request\DocumentFilterRequestValidationTrait;
use App\Types\Enums\Documents\DocumentTypeEnum;
use Illuminate\Foundation\Http\FormRequest;

class GetFilterItemsRequest extends FormRequest
{
    use DocumentFilterRequestValidationTrait;

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return $this->validationRules();
    }

    protected function prepareForValidation()
    {
        // Decode the filter_params JSON string into an associative array
        $filterParamsDecoded = json_decode($this->input('filter_params'), true);

        if (is_array($filterParamsDecoded)) {

            // Add contract types to the filter_params
            $documentType = DocumentTypeEnum::from($this->route('type'));
            $filterParamsDecoded['contract_types'] = $documentType
                ->contractTypes()
                ->map(fn ($contractType) => $contractType->value)
                ->toArray();

            $this->merge(['filter_params' => $filterParamsDecoded]);
        }

    }
}
