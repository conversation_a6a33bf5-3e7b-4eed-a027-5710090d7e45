<?php

declare(strict_types=1);

namespace App\Http\Resources\Documents;

use Illuminate\Http\Resources\Json\ResourceCollection;

class ContractCollection extends ResourceCollection
{
    /**
     * The resource that this resource collects.
     */
    public $collects = ContractResource::class;

    /**
     * Transform the resource collection into an array.
     */
    public function toArray($request): array
    {
        $total = $this->collection->count();

        return [
            'rows' => $this->collection,
            'total' => $this->additional['total'] ?? null,
            'footer' => $this->additional['footer'] ?? [],
        ];
    }

    /**
     * Add additional metadata to the resource collection.
     */
    public function additional(array $data): self
    {
        $this->additional = $data;

        return $this;
    }
}
