<?php

declare(strict_types=1);

namespace App\Http\Resources\Documents;

use App\Types\Enums\Documents\ContractTypeEnum as ContractType;
use App\Types\Enums\Documents\ContractCardTypeEnum;
use Illuminate\Http\Resources\Json\JsonResource;

class ContractResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray($request): array
    {
        $details = $this->resource['details'];
        $plots = $this->resource['plots'];

        return [
            ...$this->resource,
            'details' => [
                ...$details, // Keep all existing details data
                'cards' => $this->buildDetailsCards($details), // Add cards
            ],
            'plots' => [
                ...$plots, // Keep all existing plots data
                'columns' => $this->getPlotsColumns(),
            ],
        ];
    }

    private function buildDetailsCards(array $details): array
    {
        $contractType = $details['c_type'];

        return collect([
            // Annuity card for types Rent, Lease, JointProcessing, and Sublease
            [
                'condition' => in_array($contractType, [
                    ContractType::Rent->value,
                    ContractType::Lease->value,
                    ContractType::JointProcessing->value,
                    ContractType::Sublease->value
                ]),
                'builder' => fn() => $this->buildAnnuityCard($details)
            ],
            // Registration Service card for all types EXCEPT JointProcessing
            [
                'condition' => $contractType !== ContractType::JointProcessing->value,
                'builder' => fn() => $this->buildRegistrationServiceCard($details)
            ],
            // Notarial deed card for Sales, Mortgage, and Ownership
            [
                'condition' => in_array($contractType, [
                    ContractType::Sales->value,
                    ContractType::Mortgage->value,
                    ContractType::Ownership->value
                ]),
                'builder' => fn() => $this->buildNotarialDeedCard($details)
            ],
            // OSZ card for types Rent, and Lease
            [
                'condition' => in_array($contractType, [
                    ContractType::Rent->value,
                    ContractType::Lease->value
                ]),
                'builder' => fn() => $this->buildOSZCard($details)
            ]
        ])
        ->filter(fn($card) => $card['condition'])
        ->map(fn($card) => $card['builder']())
        ->values()
        ->toArray();
    }

    private function buildAnnuityCard(array $details): array
    {
        $items = collect([
            // Cash rent - check if we have overall_renta or renta
            [
                'condition' => !empty($details['overall_renta']),
                'item' => [
                    'title' => 'In cash-fixed',
                    'value' => [
                        [
                            'name' => null,
                            'unit' => 'lv/dka',
                            'quantity' => $details['overall_renta'],
                        ],
                    ],
                ]
            ],
            [
                'condition' => empty($details['overall_renta']) && !empty($details['renta']),
                'item' => [
                    'title' => 'In cash',
                    'value' => [
                        [
                            'name' => null,
                            'unit' => 'lv/dka',
                            'quantity' => $details['renta'],
                        ],
                    ],
                ]
            ]
        ])
        ->filter(fn($entry) => $entry['condition'])
        ->map(fn($entry) => $entry['item'])
        ->push([
            'title' => 'In kind',
            'value' => $details['renta_nat'] ?? [],
        ])
        ->values()
        ->toArray();

        return [
            'title' => ContractCardTypeEnum::Annuity->value,
            'items' => $items,
        ];
    }

    private function buildRegistrationServiceCard(array $details): array
    {
        $items = collect([
            ['title' => 'Contract number', 'field' => 'sv_num'],
            ['title' => 'Date', 'field' => 'sv_date'],
        ])
        ->map(fn($config) => $this->buildCardItem($config['title'], $config['field'], $details))
        ->toArray();

        return [
            'title' => ContractCardTypeEnum::RegistrationService->value,
            'items' => $items,
        ];
    }

    private function buildNotarialDeedCard(array $details): array
    {
        $items = collect([
            ['title' => 'Notarial number', 'field' => 'na_num'],
            ['title' => 'Delo', 'field' => 'delo'],
            ['title' => 'Tom', 'field' => 'tom'],
            ['title' => 'Court', 'field' => 'court'],
        ])
        ->map(fn($config) => $this->buildCardItem($config['title'], $config['field'], $details))
        ->toArray();

        return [
            'title' => ContractCardTypeEnum::NotarialDeed->value,
            'items' => $items,
        ];
    }

    private function buildOSZCard(array $details): array
    {
        $items = collect([
            ['title' => 'Number', 'field' => 'osz_num'],
            ['title' => 'Date', 'field' => 'osz_date'],
        ])
        ->map(fn($config) => $this->buildCardItem($config['title'], $config['field'], $details))
        ->toArray();

        return [
            'title' => ContractCardTypeEnum::OSZ->value,
            'items' => $items,
        ];
    }

    private function getPlotsColumns(): array
    {
        $contractType = $this->resource['details']['c_type'];

        return match($contractType) {
            ContractType::Sales->value => $this->getSalesContractColumns(),
            ContractType::Mortgage->value => $this->getMortgageContractColumns(),
            ContractType::Ownership->value => $this->getOwnershipContractColumns(),
            ContractType::Sublease->value => $this->getSubleaseContractColumns(),
            default => $this->getDefaultContractColumns()
        };
    }

    private function createColumn(array $config): array
    {
        return [
            'name' => $config['name'] ?? null,
            'title' => $config['title'] ?? [],
            'sortable' => $config['sortable'] ?? true,
            'tooltip' => isset($config['title']) ? [
                'title' => $config['title'],
                'placement' => 'topLeft',
            ] : null,
            'rowspan' => $config['rowspan'] ?? 2,
            'colspan' => $config['colspan'] ?? null,
            'width' => $config['width'] ?? null,
            'children' => $config['children'] ?? null,
        ];
    }

    private function getBaseColumns(string $width = '8.8%'): array
    {
        return [
            [
                'name' => null,
                'title' => [],
                'sortable' => false,
                'tooltip' => null,
                'rowspan' => 2,
                'width' => '5%',
            ],
            [
                'name' => 'kad_ident',
                'title' => ['Identifier'],
                'width' => '15%',
            ],
            [
                'name' => 'ekatte_name',
                'title' => ['Land'],
                'width' => $width,
            ],
        ];
    }

    private function getSalesContractColumns(): array
    {
        $baseColumns = collect($this->getBaseColumns('10'))
            ->map(fn($column) => $this->createColumn($column));

        $specificColumns = collect([
            [
                'name' => 'area_group',
                'title' => ['Area', '(dka)'],
                'sortable' => false,
                'rowspan' => null,
                'colspan' => 2,
                'children' => [
                    ['name' => 'contract_area', 'title' => ['sales'], 'width' => '10'],
                    ['name' => 'document_area', 'title' => ['by document'], 'width' => '10'],
                ],
            ],
            ['name' => 'price', 'title' => ['Sold area', '(dka)'], 'width' => '10'],
            ['name' => 'plot_profit', 'title' => ['Amount'], 'width' => '10'],
            ['name' => 'ntp_title', 'title' => ['NTP'], 'width' => '10'],
            ['name' => 'category_title', 'title' => ['Category'], 'width' => '10'],
            ['name' => 'comment', 'title' => ['Note'], 'width' => '10%'],
        ])
        ->map(fn($column) => $this->createColumn($column));

        return $baseColumns->concat($specificColumns)->toArray();
    }

    private function getDefaultContractColumns(): array
    {
        $baseColumns = collect($this->getBaseColumns())
            ->map(fn($column) => $this->createColumn($column));

        $specificColumns = collect([
            [
                'name' => 'area_group',
                'title' => ['Area', '(dka)'],
                'sortable' => false,
                'rowspan' => null,
                'colspan' => 4,
                'children' => [
                    ['name' => 'contract_area', 'title' => ['by contract'], 'width' => '8.8%'],
                    ['name' => 'area_for_rent', 'title' => ['for rent'], 'width' => '8.8%'],
                    ['name' => 'allowable_area', 'title' => ['allowable'], 'width' => '8.8%'],
                    ['name' => 'document_area', 'title' => ['by document'], 'width' => '8.8%'],
                ],
            ],
            ['name' => 'rent_per_plot', 'title' => ['Rent per plot'], 'width' => '8.8%'],
            ['name' => 'ntp_title', 'title' => ['NTP'], 'width' => '8.8%'],
            ['name' => 'category_title', 'title' => ['Category'], 'width' => '8.8%'],
            ['name' => 'comment', 'title' => ['Note'], 'width' => '8.8%'],
        ])
        ->map(fn($column) => $this->createColumn($column));

        return $baseColumns->concat($specificColumns)->toArray();
    }

    private function getMortgageContractColumns(): array
    {
        $baseColumns = collect($this->getBaseColumns('13%'))
            ->map(fn($column) => $this->createColumn($column));

        $specificColumns = collect([
            [
                'name' => 'area_group',
                'title' => ['Area', '(dka)'],
                'sortable' => false,
                'rowspan' => null,
                'colspan' => 2,
                'children' => [
                    ['name' => 'contract_area', 'title' => ['mortgaged'], 'width' => '13%'],
                    ['name' => 'document_area', 'title' => ['by document'], 'width' => '13%'],
                ],
            ],
            ['name' => 'ntp_title', 'title' => ['NTP'], 'width' => '13%'],
            ['name' => 'category_title', 'title' => ['Category'], 'width' => '13%'],
            ['name' => 'comment', 'title' => ['Note'], 'width' => '13%'],
        ])
        ->map(fn($column) => $this->createColumn($column));

        return $baseColumns->concat($specificColumns)->toArray();
    }

    private function getOwnershipContractColumns(): array
    {
        $baseColumns = collect($this->getBaseColumns('9%'))
            ->map(fn($column) => $this->createColumn($column));

        $specificColumns = collect([
            [
                'name' => 'area_group',
                'title' => ['Area', '(dka)'],
                'sortable' => false,
                'rowspan' => null,
                'colspan' => 3,
                'children' => [
                    ['name' => 'contract_area', 'title' => ['own'], 'width' => '9%'],
                    ['name' => 'allowable_area', 'title' => ['allowable'], 'width' => '9%'],
                    ['name' => 'document_area', 'title' => ['by document'], 'width' => '9%'],
                ],
            ],
            ['name' => 'price', 'title' => ['Sold area', '(dka)'], 'width' => '9%'],
            ['name' => 'plot_profit', 'title' => ['Amount'], 'width' => '9%'],
            ['name' => 'ntp_title', 'title' => ['NTP'], 'width' => '9%'],
            ['name' => 'category_title', 'title' => ['Category'], 'width' => '9%'],
            ['name' => 'comment', 'title' => ['Note'], 'width' => '9%'],
        ])
        ->map(fn($column) => $this->createColumn($column));

        return $baseColumns->concat($specificColumns)->toArray();
    }

    private function getSubleaseContractColumns(): array
    {
        $baseColumns = collect($this->getBaseColumns('11.4%'))
            ->map(fn($column) => $this->createColumn($column));

        $specificColumns = collect([
            [
                'name' => 'area_group',
                'title' => ['Area', '(dka)'],
                'sortable' => false,
                'rowspan' => null,
                'colspan' => 3,
                'children' => [
                    ['name' => 'contract_area', 'title' => ['sublease'], 'width' => '11.4%'],
                    ['name' => 'area_for_rent', 'title' => ['for rent'], 'width' => '11.4%'],
                    ['name' => 'document_area', 'title' => ['by document'], 'width' => '11.4%'],
                ],
            ],
            ['name' => 'ntp_title', 'title' => ['NTP'], 'width' => '11.4%'],
            ['name' => 'category_title', 'title' => ['Category'], 'width' => '11.4%'],
            ['name' => 'comment', 'title' => ['Note'], 'width' => '11.4%'],
        ])
        ->map(fn($column) => $this->createColumn($column));

        return $baseColumns->concat($specificColumns)->toArray();
    }

    private function buildCardItem(string $title, string $field, array $details, ?string $unit = null): array
    {
        return [
            'title' => $title,
            'value' => [
                [
                    'name' => null,
                    'unit' => $unit,
                    'quantity' => $details[$field] ?? '',
                ],
            ],
        ];
    }
}
