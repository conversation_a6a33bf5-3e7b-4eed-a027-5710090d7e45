<?php

declare(strict_types=1);

namespace App\Http\Resources\Documents;

use App\Types\Enums\Documents\ContractTypeEnum as ContractType;
use App\Types\Enums\Documents\ContractCardTypeEnum;
use Illuminate\Http\Resources\Json\JsonResource;

class ContractResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray($request): array
    {
        $details = $this->resource['details'];
        $plots = $this->resource['plots'];

        return [
            ...$this->resource,
            'details' => [
                ...$details, // Keep all existing details data
                'cards' => $this->buildDetailsCards($details), // Add cards
            ],
            'plots' => [
                ...$plots, // Keep all existing plots data
                'columns' => $this->getPlotsColumns(),
            ],
        ];
    }

    private function buildDetailsCards(array $details): array
    {
        $cards = [];
        $contractType = $details['c_type'];

        // Annuity card for types Rent, Lease, JointProcessing, and Sublease
        if (in_array($contractType, [ContractType::Rent->value, ContractType::Lease->value, ContractType::JointProcessing->value, ContractType::Sublease->value])) {
            $cards[] = $this->buildAnnuityCard($details);
        }

        // Registration Service card for all types EXCEPT JointProcessing
        if ($contractType !== ContractType::JointProcessing->value) {
            $cards[] = $this->buildRegistrationServiceCard($details);
        }

        // Notarial deed card for Sales, Mortgage, and Ownership
        if (in_array($contractType, [ContractType::Sales->value, ContractType::Mortgage->value, ContractType::Ownership->value])) {
            $cards[] = $this->buildNotarialDeedCard($details);
        }

        // OSZ card for types Rent, and Lease
        if (in_array($contractType, [ContractType::Rent->value, ContractType::Lease->value])) {
            $cards[] = $this->buildOSZCard($details);
        }

        return $cards;
    }

    private function buildAnnuityCard(array $details): array
    {
        $items = [];

        // Cash rent - check if we have overall_renta or renta
        if (! empty($details['overall_renta'])) {
            $items[] = [
                'title' => 'In cash-fixed',
                'value' => [
                    [
                        'name' => null,
                        'unit' => 'lv/dka',
                        'quantity' => $details['overall_renta'],
                    ],
                ],
            ];
        } elseif (! empty($details['renta'])) {
            $items[] = [
                'title' => 'In cash',
                'value' => [
                    [
                        'name' => null,
                        'unit' => 'lv/dka',
                        'quantity' => $details['renta'],
                    ],
                ],
            ];
        }

        // In kind rent - use directly from database as it's already in the correct format
        $items[] = [
            'title' => 'In kind',
            'value' => $details['renta_nat'] ?? [],
        ];

        return [
            'title' => ContractCardTypeEnum::Annuity->value,
            'items' => $items,
        ];
    }

    private function buildRegistrationServiceCard(array $details): array
    {
        return [
            'title' => ContractCardTypeEnum::RegistrationService->value,
            'items' => [
                [
                    'title' => 'Contract number',
                    'value' => [
                        [
                            'name' => null,
                            'unit' => null,
                            'quantity' => $details['sv_num'] ?? '',
                        ],
                    ],
                ],
                [
                    'title' => 'Date',
                    'value' => [
                        [
                            'name' => null,
                            'unit' => null,
                            'quantity' => $details['sv_date'] ?? '',
                        ],
                    ],
                ],
            ],
        ];
    }

    private function buildNotarialDeedCard(array $details): array
    {
        return [
            'title' => ContractCardTypeEnum::NotarialDeed->value,
            'items' => [
                [
                    'title' => 'Notarial number',
                    'value' => [
                        [
                            'name' => null,
                            'unit' => null,
                            'quantity' => $details['na_num'] ?? '',
                        ],
                    ],
                ],
                [
                    'title' => 'Delo',
                    'value' => [
                        [
                            'name' => null,
                            'unit' => null,
                            'quantity' => $details['delo'] ?? '',
                        ],
                    ],
                ],
                [
                    'title' => 'Tom',
                    'value' => [
                        [
                            'name' => null,
                            'unit' => null,
                            'quantity' => $details['tom'] ?? '',
                        ],
                    ],
                ],
                [
                    'title' => 'Court',
                    'value' => [
                        [
                            'name' => null,
                            'unit' => null,
                            'quantity' => $details['court'] ?? '',
                        ],
                    ],
                ],
            ],
        ];
    }

    private function buildOSZCard(array $details): array
    {
        return [
            'title' => ContractCardTypeEnum::OSZ->value,
            'items' => [
                [
                    'title' => 'Number',
                    'value' => [
                        [
                            'name' => null,
                            'unit' => null,
                            'quantity' => $details['osz_num'] ?? '',
                        ],
                    ],
                ],
                [
                    'title' => 'Date',
                    'value' => [
                        [
                            'name' => null,
                            'unit' => null,
                            'quantity' => $details['osz_date'] ?? '',
                        ],
                    ],
                ],
            ],
        ];
    }

    private function getPlotsColumns(): array
    {
        $contractType = $this->resource['details']['c_type'];

        return match($contractType) {
            ContractType::Sales->value => $this->getSalesContractColumns(),
            ContractType::Mortgage->value => $this->getMortgageContractColumns(),
            ContractType::Ownership->value => $this->getOwnershipContractColumns(),
            ContractType::Sublease->value => $this->getSubleaseContractColumns(),
            default => $this->getDefaultContractColumns()
        };
    }

    private function getSalesContractColumns(): array
    {
        return [
            [
                'name' => null,
                'title' => [],
                'sortable' => false,
                'tooltip' => null,
                'rowspan' => 2,
                'colspan' => null,
                'width' => '5%',
            ],
            [
                'name' => 'kad_ident',
                'title' => ['Identifier'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Identifier'],
                    'placement' => 'topLeft',
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '15%',
            ],
            [
                'name' => 'ekatte_name',
                'title' => ['Land'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Land'],
                    'placement' => 'topLeft',
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '10',
            ],
            [
                'name' => 'area_group',
                'title' => ['Area', '(dka)'],
                'sortable' => false,
                'tooltip' => [
                    'title' => ['Area', '(dka)'],
                    'placement' => 'topLeft',
                ],
                'rowspan' => null,
                'colspan' => 2,
                'width' => null,
                'children' => [
                    [
                        'name' => 'contract_area',
                        'title' => ['sales'],
                        'sortable' => true,
                        'tooltip' => [
                            'title' => ['sales'],
                            'placement' => 'topLeft',
                        ],
                        'width' => '10',
                    ],
                    [
                        'name' => 'document_area',
                        'title' => ['by document'],
                        'sortable' => true,
                        'tooltip' => [
                            'title' => ['by document'],
                            'placement' => 'topLeft',
                        ],
                        'width' => '10',
                    ],
                ],
            ],
            [
                'name' => 'price',
                'title' => ['Sold area', '(dka)'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Sold area', '(dka)'],
                    'placement' => 'topLeft',
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '10',
            ],
            [
                'name' => 'plot_profit',
                'title' => ['Amount'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Amount'],
                    'placement' => 'topLeft',
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '10',
            ],
            [
                'name' => 'ntp_title',
                'title' => ['NTP'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['NTP'],
                    'placement' => 'topLeft',
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '10',
            ],
            [
                'name' => 'category_title',
                'title' => ['Category'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Category'],
                    'placement' => 'topLeft',
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '10',
            ],
            [
                'name' => 'comment',
                'title' => ['Note'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Note'],
                    'placement' => 'topLeft',
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '10%',
            ],
        ];
    }

    private function getDefaultContractColumns(): array
    {
        return [
            [
                'name' => null,
                'title' => [],
                'sortable' => false,
                'tooltip' => null,
                'rowspan' => 2,
                'colspan' => null,
                'width' => '5%',
            ],
            [
                'name' => 'kad_ident',
                'title' => ['Identifier'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Identifier'],
                    'placement' => 'topLeft',
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '15%',
            ],
            [
                'name' => 'ekatte_name',
                'title' => ['Land'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Land'],
                    'placement' => 'topLeft',
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '8.8%',
            ],
            [
                'name' => 'area_group',
                'title' => ['Area', '(dka)'],
                'sortable' => false,
                'tooltip' => [
                    'title' => ['Area', '(dka)'],
                    'placement' => 'topLeft',
                ],
                'rowspan' => null,
                'colspan' => 4,
                'width' => null,
                'children' => [
                    [
                        'name' => 'contract_area',
                        'title' => ['by contract'],
                        'sortable' => true,
                        'tooltip' => [
                            'title' => ['by contract'],
                            'placement' => 'topLeft',
                        ],
                        'width' => '8.8%',
                    ],
                    [
                        'name' => 'area_for_rent',
                        'title' => ['for rent'],
                        'sortable' => true,
                        'tooltip' => [
                            'title' => ['for rent'],
                            'placement' => 'topLeft',
                        ],
                        'width' => '8.8%',
                    ],
                    [
                        'name' => 'allowable_area',
                        'title' => ['allowable'],
                        'sortable' => true,
                        'tooltip' => [
                            'title' => ['allowable'],
                            'placement' => 'topLeft',
                        ],
                        'width' => '8.8%',
                    ],
                    [
                        'name' => 'document_area',
                        'title' => ['by document'],
                        'sortable' => true,
                        'tooltip' => [
                            'title' => ['by document'],
                            'placement' => 'topLeft',
                        ],
                        'width' => '8.8%',
                    ],
                ],
            ],
            [
                'name' => 'rent_per_plot',
                'title' => ['Rent per plot'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Rent per plot'],
                    'placement' => 'topLeft',
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '8.8%',
            ],
            [
                'name' => 'ntp_title',
                'title' => ['NTP'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['NTP'],
                    'placement' => 'topLeft',
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '8.8%',
            ],
            [
                'name' => 'category_title',
                'title' => ['Category'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Category'],
                    'placement' => 'topLeft',
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '8.8%',
            ],
            [
                'name' => 'comment',
                'title' => ['Note'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Note'],
                    'placement' => 'topLeft',
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '8.8%',
            ],
        ];
    }

    private function getMortgageContractColumns(): array
    {
        return [
            [
                'name' => null,
                'title' => [],
                'sortable' => false,
                'tooltip' => null,
                'rowspan' => 2,
                'colspan' => null,
                'width' => '5%',
            ],
            [
                'name' => 'kad_ident',
                'title' => ['Identifier'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Identifier'],
                    'placement' => 'topLeft',
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '15%',
            ],
            [
                'name' => 'ekatte_name',
                'title' => ['Land'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Land'],
                    'placement' => 'topLeft',
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '13%',
            ],
            [
                'name' => 'area_group',
                'title' => ['Area', '(dka)'],
                'sortable' => false,
                'tooltip' => [
                    'title' => ['Area', '(dka)'],
                    'placement' => 'topLeft',
                ],
                'rowspan' => null,
                'colspan' => 2,
                'width' => null,
                'children' => [
                    [
                        'name' => 'contract_area',
                        'title' => ['mortgaged'],
                        'sortable' => true,
                        'tooltip' => [
                            'title' => ['mortgaged'],
                            'placement' => 'topLeft',
                        ],
                        'width' => '13%',
                    ],
                    [
                        'name' => 'document_area',
                        'title' => ['by document'],
                        'sortable' => true,
                        'tooltip' => [
                            'title' => ['by document'],
                            'placement' => 'topLeft',
                        ],
                        'width' => '13%',
                    ],
                ],
            ],
            [
                'name' => 'ntp_title',
                'title' => ['NTP'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['NTP'],
                    'placement' => 'topLeft',
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '13%',
            ],
            [
                'name' => 'category_title',
                'title' => ['Category'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Category'],
                    'placement' => 'topLeft',
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '13%',
            ],
            [
                'name' => 'comment',
                'title' => ['Note'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Note'],
                    'placement' => 'topLeft',
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '13%',
            ],
        ];
    }

    private function getOwnershipContractColumns(): array
    {
        return [
            [
                'name' => null,
                'title' => [],
                'sortable' => false,
                'tooltip' => null,
                'rowspan' => 2,
                'colspan' => null,
                'width' => '5%',
            ],
            [
                'name' => 'kad_ident',
                'title' => ['Identifier'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Identifier'],
                    'placement' => 'topLeft',
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '15%',
            ],
            [
                'name' => 'ekatte_name',
                'title' => ['Land'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Land'],
                    'placement' => 'topLeft',
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '9%',
            ],
            [
                'name' => 'area_group',
                'title' => ['Area', '(dka)'],
                'sortable' => false,
                'tooltip' => [
                    'title' => ['Area', '(dka)'],
                    'placement' => 'topLeft',
                ],
                'rowspan' => null,
                'colspan' => 3,
                'width' => null,
                'children' => [
                    [
                        'name' => 'contract_area',
                        'title' => ['own'],
                        'sortable' => true,
                        'tooltip' => [
                            'title' => ['own'],
                            'placement' => 'topLeft',
                        ],
                        'width' => '9%',
                    ],
                    [
                        'name' => 'allowable_area',
                        'title' => ['allowable'],
                        'sortable' => true,
                        'tooltip' => [
                            'title' => ['allowable'],
                            'placement' => 'topLeft',
                        ],
                        'width' => '9%',
                    ],
                    [
                        'name' => 'document_area',
                        'title' => ['by document'],
                        'sortable' => true,
                        'tooltip' => [
                            'title' => ['by document'],
                            'placement' => 'topLeft',
                        ],
                        'width' => '9%',
                    ],
                ],
            ],
            [
                'name' => 'price',
                'title' => ['Sold area', '(dka)'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Sold area', '(dka)'],
                    'placement' => 'topLeft',
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '9%',
            ],
            [
                'name' => 'plot_profit',
                'title' => ['Amount'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Amount'],
                    'placement' => 'topLeft',
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '9%',
            ],
            [
                'name' => 'ntp_title',
                'title' => ['NTP'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['NTP'],
                    'placement' => 'topLeft',
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '9%',
            ],
            [
                'name' => 'category_title',
                'title' => ['Category'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Category'],
                    'placement' => 'topLeft',
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '9%',
            ],
            [
                'name' => 'comment',
                'title' => ['Note'],
                'sortable' => true,
                'tooltip' => [
                    'title' => ['Note'],
                    'placement' => 'topLeft',
                ],
                'rowspan' => 2,
                'colspan' => null,
                'width' => '9%',
            ],
        ];
    }

    private function getSubleaseContractColumns(): array
    {
        return [
            [
                [
                    'name' => null,
                    'title' => [],
                    'sortable' => false,
                    'tooltip' => null,
                    'rowspan' => 2,
                    'colspan' => null,
                    'width' => '5%',
                ],
                [
                    'name' => 'kad_ident',
                    'title' => ['Identifier'],
                    'sortable' => true,
                    'tooltip' => [
                        'title' => ['Identifier'],
                        'placement' => 'topLeft',
                    ],
                    'rowspan' => 2,
                    'colspan' => null,
                    'width' => '15%',
                ],
                [
                    'name' => 'ekatte_name',
                    'title' => ['Land'],
                    'sortable' => true,
                    'tooltip' => [
                        'title' => ['Land'],
                        'placement' => 'topLeft',
                    ],
                    'rowspan' => 2,
                    'colspan' => null,
                    'width' => '11.4%',
                ],
                [
                    'name' => 'area_group',
                    'title' => ['Area', '(dka)'],
                    'sortable' => false,
                    'tooltip' => [
                        'title' => ['Area', '(dka)'],
                        'placement' => 'topLeft',
                    ],
                    'rowspan' => null,
                    'colspan' => 3,
                    'width' => null,
                    'children' => [
                        [
                            'name' => 'contract_area',
                            'title' => ['sublease'],
                            'sortable' => true,
                            'tooltip' => [
                                'title' => ['sublease'],
                                'placement' => 'topLeft',
                            ],
                            'width' => '11.4%',
                        ],
                        [
                            'name' => 'area_for_rent',
                            'title' => ['for rent'],
                            'sortable' => true,
                            'tooltip' => [
                                'title' => ['for rent'],
                                'placement' => 'topLeft',
                            ],
                            'width' => '11.4%',
                        ],
                        [
                            'name' => 'document_area',
                            'title' => ['by document'],
                            'sortable' => true,
                            'tooltip' => [
                                'title' => ['by document'],
                                'placement' => 'topLeft',
                            ],
                            'width' => '11.4%',
                        ],
                    ],
                ],
                [
                    'name' => 'ntp_title',
                    'title' => ['NTP'],
                    'sortable' => true,
                    'tooltip' => [
                        'title' => ['NTP'],
                        'placement' => 'topLeft',
                    ],
                    'rowspan' => 2,
                    'colspan' => null,
                    'width' => '11.4%',
                ],
                [
                    'name' => 'category_title',
                    'title' => ['Category'],
                    'sortable' => true,
                    'tooltip' => [
                        'title' => ['Category'],
                        'placement' => 'topLeft',
                    ],
                    'rowspan' => 2,
                    'colspan' => null,
                    'width' => '11.4%',
                ],
                [
                    'name' => 'comment',
                    'title' => ['Note'],
                    'sortable' => true,
                    'tooltip' => [
                        'title' => ['Note'],
                        'placement' => 'topLeft',
                    ],
                    'rowspan' => 2,
                    'colspan' => null,
                    'width' => '11.4%',
                ],
            ],
        ];
    }
}
