<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Models\Main\User;
use Illuminate\Database\Console\Migrations\MigrateCommand;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Migrations\Migrator;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Support\Facades\Config;

class MigrateUserDb extends MigrateCommand
{
    protected $signature = 'migrate:userdb {--database=} {--all} {--force} {--path=*} {--realpath} {--schema-path=} {--pretend} {--seed} {--step}';

    protected $description = 'Run migrations on a specific user_db or all user_db databases';

    public function __construct(Migrator $migrator, Dispatcher $dispatcher)
    {
        parent::__construct($migrator, $dispatcher);
    }

    public function handle()
    {

        $migrationPaths = $this->getMigrationPaths();

        if ($this->option('all')) {
            // Get all existing user databases and run migrations on each
            $userDatabases = User::select('database')
                ->join('pg_database', 'datname', '=', 'database')
                ->where('datistemplate', false)
                ->whereLike('database', 'db_bg_%')
                ->get()->pluck('database')->unique();
            $userDatabases->push('new_users_db');

            foreach ($userDatabases as $database) {
                $this->info("Adding migrations table in database: {$database}");

                $this->runMigrationsOnDatabase($database, $migrationPaths);
            }
        } elseif ($this->option('database')) {
            // Run migrations on the specified user_db
            $database = $this->option('database');
            $this->info("Running migrations on database: {$database}");
            $this->runMigrationsOnDatabase($database, $migrationPaths);
        } else {
            $this->error('You must specify either the --database option or the --all option.');

            return 1; // Exit with an error code
        }

        return 0; // Exit with a success code
    }

    protected function getMigrationPaths(): array
    {
        return [database_path('migrations/user_db')];
    }

    protected function runMigrationsOnDatabase($database, $migrationPaths)
    {
        Config::set('database.connections.user_db.database', $database);
        DB::reconnect('user_db');

        $this->migrator->setConnection('user_db');

        // Get the migration files
        $files = $this->migrator->getMigrationFiles($migrationPaths);

        // Run the migrations
        $this->migrator->run($files, [
            'pretend' => $this->option('pretend'),
            'step' => $this->option('step'),
        ]);
    }
}
