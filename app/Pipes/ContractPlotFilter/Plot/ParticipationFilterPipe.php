<?php

declare(strict_types=1);

namespace App\Pipes\ContractPlotFilter\Plot;

use Closure;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class ParticipationFilterPipe extends BasePlotFilterPipe
{
    public function __construct(protected ?array $filterValues)
    {
        $this->filterColumn =  "
            CASE
                WHEN layer_kvs.participate = TRUE AND layer_kvs.include = FALSE AND layer_kvs.white_spots = FALSE
                    THEN 'Participate'
                WHEN layer_kvs.participate = FALSE AND layer_kvs.include = TRUE AND layer_kvs.white_spots = FALSE
                    THEN 'No participate'
                WHEN layer_kvs.participate = FALSE AND layer_kvs.include = FALSE AND layer_kvs.white_spots = TRUE 
                    THEN 'White spots'
                ELSE 
                    'Without'
            END
        ";
    }

    public function handle(Builder $query, Closure $next)
    {
        if (empty($this->filterValues)) {
            return $next($query);
        }

        $this->joinRequiredTables($query);

        $query->whereIn(
            DB::raw($this->filterColumn),
            $this->filterValues
        );

        return $next($query);
    }
}
