<?php

declare(strict_types=1);

namespace App\Pipes\ContractPlotFilter\Plot;

use App\Types\Enums\Documents\ContractTypeEnum;
use App\Types\Interfaces\ContractPlot\ContractPlotFilterPipe;
use Closure;
use Exception;
use Illuminate\Database\Query\Builder;

class VirtualContractTypeFilterPipe implements ContractPlotFilterPipe
{
    public function __construct(protected ?array $filterValues) {}

    public function handle(Builder $query, Closure $next)
    {
        if (empty($this->filterValues)) {
            return $next($query);
        }

        $this->joinRequiredTables($query);

        if ('su_sales_contracts' === $query->from) {
            // Cannot filter sales contracts by this filter
            return $next($query);
        }

        $hasNullValue = in_array(null, $this->filterValues);
        $filterValues = array_filter($this->filterValues, fn ($value) => null !== $value);

        if ('su_contracts' === $query->from) {
            $query->where(function ($query) use ($filterValues, $hasNullValue) {
                $query->whereIn('su_contracts.virtual_contract_type', $filterValues)
                    ->when(
                        $hasNullValue,
                        fn () => $query->orWhereNull('su_contracts.virtual_contract_type')
                    );
            });

            return $next($query);

        }

        if ('layer_kvs' === $query->from) {
            $query->where(function ($query) use ($filterValues, $hasNullValue) {
                $hasSalesContractType = in_array(ContractTypeEnum::Sales->name(), $filterValues);
                $filterValues = array_filter($filterValues, fn ($value) => ContractTypeEnum::Sales->name() !== $value);

                $query->whereIn('su_contracts.virtual_contract_type', $filterValues)
                    ->when(
                        $hasSalesContractType,
                        fn () => $query->orWhereNotNull('su_sales_contracts.id')
                    )
                    ->when(
                        $hasNullValue,
                        fn () => $query->orWhere(function ($query) {
                            $query->whereNull('su_contracts.virtual_contract_type');
                            $query->whereNull('su_sales_contracts.id');
                        })
                    );

            });

            return $next($query);
        }

        throw new Exception('Invalid base table');
    }

    public function handleFilterItems(Builder $query, Closure $next)
    {
        $salesContractType = ContractTypeEnum::Sales->name();
        $valueExpression = match($query->from) {
            'su_contracts' => "{$query->from}.virtual_contract_type",
            'su_sales_contracts' => throw new Exception('Cannot filter sales contracts by virtual contract type'),
            'layer_kvs' => "UNNEST(
                ARRAY_REMOVE(
                    ARRAY[su_contracts.virtual_contract_type, '{$salesContractType}'],
                    NULL
                )
            )",
            default => throw new Exception('Invalid table name for virtual contract type filter')
        };

        $this->joinRequiredTables($query);
        $query->selectRaw("{$valueExpression} AS value, {$valueExpression} AS label")->distinct();

        return $next($query);
    }

    public function joinRequiredTables(Builder $query): void
    {
        $joinedTables = collect($query->joins)->pluck('table');

        match($query->from) {
            'su_contracts',
            'su_sales_contracts' => $query,
            'layer_kvs' => $query
                ->when(
                    ! $joinedTables->contains('su_contracts_plots_rel'),
                    fn () => $query->leftJoin('su_contracts_plots_rel', 'su_contracts_plots_rel.plot_id', '=', 'layer_kvs.gid')
                )
                ->when(
                    ! $joinedTables->contains('su_contracts'),
                    fn () => $query->leftJoin('su_contracts', 'su_contracts.id', '=', 'su_contracts_plots_rel.contract_id')
                )
                ->when(
                    ! $joinedTables->contains('su_sales_contracts_plots_rel'),
                    fn () =>             $query->leftJoin('su_contracts_plots_rel', 'su_contracts_plots_rel.plot_id', '=', 'layer_kvs.gid')
                )
                ->when(
                    ! $joinedTables->contains('su_sales_contracts'),
                    fn () => $query->leftJoin('su_sales_contracts', 'su_sales_contracts.id', '=', 'su_sales_contracts_plots_rel.sales_contract_id')
                ),
            default => throw new Exception('Error joining tables for filtering by virtyal contract type')
        };
    }
}
