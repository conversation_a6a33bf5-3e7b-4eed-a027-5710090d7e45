<?php

declare(strict_types=1);

namespace App\Pipes\ContractPlotFilter\Contract;

use App\Types\Enums\Documents\DocumentTypeEnum;
use Closure;
use Exception;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class FarmingYearsFilterPipe extends BaseContractFilterPipe
{
    public function __construct(private ?array $filterValues) {}

    public function handle(Builder $query, Closure $next)
    {
        if (empty($this->filterValues)) {
            return $next($query);
        }

        $this->joinRequiredTables($query);

        if (DocumentTypeEnum::tableNames()->contains($query->from)) {
            $query->where(function ($query) {
                $this->filterQuery($query, $query->from, $this->filterValues);
            });

            return $query;
        }

        if ('layer_kvs' === $query->from) {
            $query->where(function ($query) {
                $this->filterQuery($query, 'su_contracts', $this->filterValues);
                $this->filterQuery($query, 'su_sales_contracts', $this->filterValues);
            });

            return $query;
        }

        throw new Exception('Invalid base table');
    }

    public function handleFilterItems(Builder $query, Closure $next)
    {
        $className = self::class;

        $this->joinRequiredTables($query);

        $startYear = 2004;
        $endYear = 2100;

        $farmingYears = DB::table(
            DB::raw("
                GENERATE_SERIES(
				    {$startYear},
				    {$endYear},
                    1
			    ) AS farming_years(f_year)
            ")
        )
            ->selectRaw("
                f_year - 1 || '/' || f_year as f_year_text,
			    f_year,
                CASE WHEN 
		            (f_year = get_farming_year_by_date(now()::date) + 1) or (f_year = get_farming_year_by_date(now()::date))
                    THEN 1
                    ELSE 2 
	            END AS sort_priority,
	            (f_year = get_farming_year_by_date(now()::date) + 1) as has_delimiter
        ");

        $query->withExpression('farming_years', $farmingYears)
            ->selectRaw('
                farming_years.f_year_text AS value,
                farming_years.f_year_text AS label,
                farming_years.has_delimiter,
                farming_years.sort_priority
            ')
            ->join(
                'farming_years',
                fn ($join) => match($query->from) {
                    'su_contracts' => $join
                        ->on('su_contracts.start_date', '<=', DB::raw("(farming_years.f_year || '-10-01')::date"))
                        ->where('su_contracts.due_date', '>=', DB::raw("(farming_years.f_year || '-09-30')::date")),
                    'su_sales_contracts' => $join
                        ->on('su_sales_contracts.start_date', '<=', DB::raw("(farming_years.f_year || '-10-01')::date"))
                        ->where(DB::raw("COALESCE(su_sales_contracts.due_date, '9999-01-01'::DATE)"), '>=', DB::raw("(farming_years.f_year || '-09-30')::date")),
                    'layer_kvs' => $join
                        ->on('su_contracts.start_date', '<=', DB::raw("(farming_years.f_year || '-10-01')::date"))
                        ->where('su_contracts.due_date', '>=', DB::raw("(farming_years.f_year || '-09-30')::date"))
                        ->where('su_sales_contracts.start_date', '<=', DB::raw("(farming_years.f_year || '-10-01')::date"))
                        ->where(DB::raw("COALESCE(su_sales_contracts.due_date, '9999-01-01'::DATE)"), '>=', DB::raw("(farming_years.f_year || '-09-30')::date")),
                    default => throw new Exception("Invalid table for filter '{$className}'  selection")
                }
            )->distinct();

        return $next($query);
    }

    private function filterQuery(Builder $query, string $baseTable, array $filterValues)
    {

        foreach ($filterValues as $value) {
            if (! $value) {
                continue;
            }

            $query->orWhere(function ($subQ) use ($baseTable, $value) {
                [$startYear, $endYear] = explode('/', $value);

                $farmingYearStart = $startYear . '-10-01';
                $farmingYearEnd = $endYear . '-09-30';

                $subQ->orWhere("{$baseTable}.start_date", '<=', DB::raw("'{$farmingYearStart}'::date"))
                    ->where(function ($subQ) use ($farmingYearEnd, $baseTable) {
                        $subQ->where("{$baseTable}.due_date", '>=', DB::raw("'{$farmingYearEnd}'::date"))
                            ->orWhereNull("{$baseTable}.due_date");
                    });
            });
        }
    }
}
