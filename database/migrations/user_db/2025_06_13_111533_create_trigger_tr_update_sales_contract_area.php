<?php

declare(strict_types=1);

use App\Database\Migrations\UserDatabaseMigration;
use Illuminate\Support\Facades\DB;

return new class () extends UserDatabaseMigration {
    /**
     * Run the migrations.
     */
    public function up()
    {
        // Create the trigger function that will be executed by the trigger for sales contracts.
        // This function determines the correct sales_contract_id, calls the calculation function,
        // and then updates the 'contract_area' column in the 'su_sales_contracts' table.
        DB::unprepared("
            CREATE OR REPLACE FUNCTION update_su_sales_contracts_contract_area()
            RETURNS TRIGGER AS $$
            BEGIN
                -- Update the 'contract_area' column in the corresponding sales contracts.
                IF (TG_OP = 'DELETE') THEN
                    UPDATE su_sales_contracts
                    SET contract_area = calculate_sales_contract_area(OLD.sales_contract_id)
                    WHERE id = OLD.sales_contract_id;
                ELSIF (TG_OP = 'UPDATE') THEN
                    UPDATE su_sales_contracts
                    SET contract_area = calculate_sales_contract_area(NEW.sales_contract_id)
                    WHERE id = NEW.sales_contract_id;

                    UPDATE su_sales_contracts
                    SET contract_area = calculate_sales_contract_area(OLD.sales_contract_id)
                    WHERE id = OLD.sales_contract_id;
                ELSE
                    UPDATE su_sales_contracts
                    SET contract_area = calculate_sales_contract_area(NEW.sales_contract_id)
                    WHERE id = NEW.sales_contract_id;
                END IF;

                -- Return the appropriate record to complete the trigger operation.
                IF (TG_OP = 'DELETE') THEN
                    RETURN OLD;
                ELSE
                    RETURN NEW;
                END IF;
            END;
            $$ LANGUAGE plpgsql;
        ");

        // Create the trigger on the 'su_sales_contracts_plots_rel' table.
        // It fires AFTER an INSERT, DELETE, or UPDATE of the 'contract_area' column.
        DB::unprepared('
            CREATE TRIGGER tr_update_sales_contract_area
            AFTER INSERT OR DELETE OR UPDATE OF contract_area, sales_contract_id ON su_sales_contracts_plots_rel
            FOR EACH ROW EXECUTE FUNCTION update_su_sales_contracts_contract_area();
        ');

        // Initialize the contract_area for existing records in su_sales_contracts_plots_rel.
        DB::unprepared('UPDATE su_sales_contracts SET contract_area = calculate_sales_contract_area(id);');
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        // To roll back, we must first drop the trigger from the table,
        // and then drop the trigger function itself.
        DB::unprepared('DROP TRIGGER IF EXISTS tr_update_sales_contract_area ON su_sales_contracts_plots_rel;');
        DB::unprepared('DROP FUNCTION IF EXISTS update_su_sales_contracts_contract_area();');
    }
};
