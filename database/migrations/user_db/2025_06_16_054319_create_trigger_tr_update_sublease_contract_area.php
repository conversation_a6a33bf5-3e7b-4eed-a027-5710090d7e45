<?php

declare(strict_types=1);

use App\Database\Migrations\UserDatabaseMigration;
use Illuminate\Support\Facades\DB;

return new class () extends UserDatabaseMigration {
    /**
     * Run the migrations.
     */
    public function up()
    {
        // Create the trigger function
        // This function handles the logic for getting the correct contract_id
        // based on the operation (INSERT, UPDATE, or DELETE) and then
        // calls the 'calculate_sublease_contract_area' function to get the new total area.
        // It then updates the 'contract_area' column in the 'su_contracts' table (only for is_sublease=TRUE).
        DB::unprepared("
            CREATE OR REPLACE FUNCTION update_sublease_contracts_contract_area()
            RETURNS TRIGGER AS $$
            BEGIN
               -- Update the 'contract_area' column in the corresponding contracts.
                IF (TG_OP = 'DELETE') THEN
                    UPDATE su_contracts
                    SET contract_area = calculate_sublease_contract_area(OLD.sublease_id)
                    WHERE id = OLD.sublease_id;
                ELSIF (TG_OP = 'UPDATE') THEN
                    UPDATE su_contracts
                    SET contract_area = calculate_sublease_contract_area(NEW.sublease_id)
                    WHERE id = NEW.sublease_id;

                    UPDATE su_contracts
                    SET contract_area = calculate_sublease_contract_area(OLD.sublease_id)
                    WHERE id = OLD.sublease_id;
                ELSE
                    UPDATE su_contracts
                    SET contract_area = calculate_sublease_contract_area(NEW.sublease_id)
                    WHERE id = NEW.sublease_id;
                END IF;

                -- Return the appropriate record to complete the trigger operation.
                IF (TG_OP = 'DELETE') THEN
                    RETURN OLD;
                ELSE
                    RETURN NEW;
                END IF;
            END;
            $$ LANGUAGE plpgsql;
        ");

        // Create the actual trigger on the 'su_subleases_plots_area' table.
        // This trigger will fire AFTER any INSERT, UPDATE, or DELETE operation,
        // for each row that is affected.
        DB::unprepared('
            CREATE TRIGGER tr_update_sublease_contract_area
            AFTER INSERT OR DELETE OR UPDATE OF contract_area, sublease_id ON su_subleases_plots_area
            FOR EACH ROW EXECUTE FUNCTION update_sublease_contracts_contract_area();
        ');


        // Initialize the contract_area for existing records in su_contracts_plots_rel.
        DB::unprepared('UPDATE su_contracts SET contract_area = calculate_sublease_contract_area(id) WHERE is_sublease=true;');
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        DB::unprepared('DROP TRIGGER IF EXISTS tr_update_sublease_contract_area ON su_subleases_plots_area;');
        DB::unprepared('DROP FUNCTION IF EXISTS update_sublease_contracts_contract_area();');
    }
};
